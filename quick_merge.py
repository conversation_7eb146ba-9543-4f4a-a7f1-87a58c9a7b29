#!/usr/bin/env python3
"""
Quick merge script to combine all the successfully collected data
"""

import pandas as pd
import os
from datetime import datetime

def quick_merge():
    print("🚀 Quick Merge of Extended Historical Data")
    print("=" * 50)
    
    # Data files to merge
    data_files = [
        'data/processed/early_altcoins_historical_data.csv',
        'data/processed/wayback_cmc_historical_data.csv', 
        'data/processed/blockchain_info_historical_data.csv',
        'data/processed/extended_historical_combined_data.csv',
        'data/processed/yahoo_finance_crypto_data.csv',
        'data/processed/cryptocompare_historical_data.csv',
        'data/processed/full_historical_data.csv'
    ]
    
    all_datasets = []
    total_records = 0
    
    for file_path in data_files:
        if os.path.exists(file_path):
            try:
                df = pd.read_csv(file_path)
                filename = os.path.basename(file_path)
                df['data_source'] = filename.replace('.csv', '')
                all_datasets.append(df)
                total_records += len(df)
                print(f"✓ Loaded {filename}: {len(df):,} records")
            except Exception as e:
                print(f"✗ Failed to load {file_path}: {str(e)}")
    
    if all_datasets:
        print(f"\n📊 Combining {len(all_datasets)} datasets...")
        combined_df = pd.concat(all_datasets, ignore_index=True)
        
        # Basic cleanup
        print("🧹 Cleaning data...")
        
        # Standardize date columns
        date_columns = ['date', 'Date', 'timestamp', 'time']
        for col in date_columns:
            if col in combined_df.columns:
                try:
                    combined_df['date_standardized'] = pd.to_datetime(combined_df[col], errors='coerce')
                    break
                except:
                    continue
        
        # Remove duplicates
        initial_count = len(combined_df)
        if 'date_standardized' in combined_df.columns and 'symbol' in combined_df.columns:
            combined_df = combined_df.drop_duplicates(subset=['date_standardized', 'symbol'], keep='first')
        else:
            combined_df = combined_df.drop_duplicates(keep='first')
        
        removed_dupes = initial_count - len(combined_df)
        print(f"🗑️  Removed {removed_dupes:,} duplicates")
        
        # Save comprehensive dataset
        os.makedirs('data/comprehensive', exist_ok=True)
        output_file = 'data/comprehensive/comprehensive_12_year_crypto_dataset.csv'
        combined_df.to_csv(output_file, index=False)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Saved: {output_file}")
        print(f"📊 Total Records: {len(combined_df):,}")
        
        # Generate quick analysis
        analysis_lines = [
            "COMPREHENSIVE 12-13 YEAR CRYPTOCURRENCY DATASET",
            "=" * 55,
            f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Records: {len(combined_df):,}",
            f"Data Sources: {len(all_datasets)}",
            "",
            "DATASET BREAKDOWN:",
            "-" * 20
        ]
        
        if 'data_source' in combined_df.columns:
            source_counts = combined_df['data_source'].value_counts()
            for source, count in source_counts.items():
                analysis_lines.append(f"  {source}: {count:,} records")
        
        if 'symbol' in combined_df.columns:
            unique_symbols = combined_df['symbol'].nunique()
            analysis_lines.extend([
                "",
                f"Unique Cryptocurrencies: {unique_symbols:,}",
                ""
            ])
            
            # Top cryptocurrencies
            top_cryptos = combined_df['symbol'].value_counts().head(10)
            analysis_lines.extend([
                "Top 10 Most Tracked Cryptocurrencies:",
                "-" * 35
            ])
            for symbol, count in top_cryptos.items():
                analysis_lines.append(f"  {symbol}: {count:,} records")
        
        if 'date_standardized' in combined_df.columns:
            min_date = combined_df['date_standardized'].min()
            max_date = combined_df['date_standardized'].max()
            if pd.notna(min_date) and pd.notna(max_date):
                years_covered = (max_date - min_date).days / 365.25
                analysis_lines.extend([
                    "",
                    "DATE COVERAGE:",
                    "-" * 15,
                    f"Earliest Date: {min_date.strftime('%Y-%m-%d')}",
                    f"Latest Date: {max_date.strftime('%Y-%m-%d')}",
                    f"Years Covered: {years_covered:.1f} years"
                ])
        
        analysis_text = "\n".join(analysis_lines)
        
        # Save analysis
        analysis_file = 'data/comprehensive/comprehensive_analysis.txt'
        with open(analysis_file, 'w') as f:
            f.write(analysis_text)
        
        print(f"📋 Analysis: {analysis_file}")
        print("\n" + "=" * 50)
        print("COMPREHENSIVE DATASET CREATED!")
        print("=" * 50)
        print(analysis_text)
        
        return True
    else:
        print("❌ No datasets found to merge")
        return False

if __name__ == "__main__":
    quick_merge()
