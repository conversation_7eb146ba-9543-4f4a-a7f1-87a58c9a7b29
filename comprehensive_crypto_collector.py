"""
Comprehensive Cryptocurrency Data Collector
Collects historical data for hundreds of cryptocurrencies from multiple free sources
"""
import os
import sys
import time
import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm
import json
from typing import List, Dict, Optional, Tuple
import concurrent.futures
from threading import Lock

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/comprehensive_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveCryptoCollector:
    """
    A comprehensive cryptocurrency data collector that gathers data from multiple free sources
    for hundreds of cryptocurrencies including top market cap tokens, DeFi tokens, and modern cryptocurrencies.
    """
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Create directories
        os.makedirs('data/processed', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        
        # Rate limiting
        self.coingecko_delay = 1.2  # seconds between CoinGecko API calls
        self.yahoo_delay = 0.5      # seconds between Yahoo Finance calls
        self.general_delay = 0.3    # seconds between other API calls
        
        # Thread safety
        self.data_lock = Lock()
        self.collected_data = []
        
        # Comprehensive token lists
        self.token_categories = self._initialize_token_categories()
        
    def _initialize_token_categories(self) -> Dict[str, List[str]]:
        """Initialize comprehensive lists of cryptocurrencies to collect"""
        return {
            'top_market_cap': [
                'BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'SOL', 'DOGE', 'DOT', 'AVAX', 'SHIB',
                'MATIC', 'LTC', 'UNI', 'LINK', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX',
                'ETC', 'XLM', 'THETA', 'HBAR', 'NEAR', 'FLOW', 'EGLD', 'MANA', 'SAND', 'AXS',
                'ATOM', 'FTM', 'XTZ', 'AAVE', 'GRT', 'MKR', 'COMP', 'YFI', 'SUSHI', 'CRV',
                'SNX', 'BAL', 'REN', 'KNC', 'ZRX', 'BAND', 'STORJ', 'NMR', 'REP', 'MLN'
            ],
            'defi_tokens': [
                'UNI', 'SUSHI', 'AAVE', 'COMP', 'MKR', 'YFI', 'CRV', '1INCH', 'SNX', 'BAL',
                'REN', 'KNC', 'ZRX', 'BAND', 'STORJ', 'NMR', 'REP', 'MLN', 'LRC', 'ALPHA',
                'CREAM', 'BADGER', 'PICKLE', 'FARM', 'HARVEST', 'ROOK', 'CVX', 'FXS', 'TRIBE',
                'OHM', 'KLIMA', 'TIME', 'SPELL', 'ICE', 'BOBA', 'OP', 'ARB', 'GMX', 'GNS'
            ],
            'layer2_scaling': [
                'MATIC', 'ARB', 'OP', 'METIS', 'BOBA', 'LRC', 'IMX', 'DYDX', 'ZKS', 'MINA'
            ],
            'meme_tokens': [
                'DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BABYDOGE', 'SAFEMOON', 'ELON', 'AKITA',
                'KISHU', 'HOKK', 'LEASH', 'BONE', 'RYOSHI', 'WOOF', 'CHEEMS', 'DOGELON'
            ],
            'exchange_tokens': [
                'BNB', 'FTT', 'CRO', 'HT', 'OKB', 'LEO', 'KCS', 'GT', 'WRX', 'BGB'
            ],
            'gaming_metaverse': [
                'AXS', 'SLP', 'MANA', 'SAND', 'ENJ', 'CHR', 'ALICE', 'TLM', 'GALA', 'ILV',
                'YGG', 'GHST', 'REVV', 'TOWER', 'NFTX', 'WHALE', 'RARI', 'SUPER', 'TVK', 'PYR'
            ],
            'infrastructure': [
                'DOT', 'KSM', 'ATOM', 'OSMO', 'JUNO', 'SCRT', 'LUNA', 'UST', 'ANCHOR', 'MIR',
                'RUNE', 'SIFCHAIN', 'ROWAN', 'DVPN', 'AKT', 'XPRT', 'NGM', 'HARD', 'SWP', 'USDX'
            ],
            'privacy_coins': [
                'XMR', 'ZEC', 'DASH', 'DCR', 'FIRO', 'BEAM', 'GRIN', 'ARRR', 'DERO', 'OXEN'
            ],
            'stablecoins': [
                'USDT', 'USDC', 'BUSD', 'DAI', 'FRAX', 'LUSD', 'USDN', 'USDP', 'TUSD', 'GUSD'
            ],
            'wrapped_tokens': [
                'WBTC', 'WETH', 'WBNB', 'WMATIC', 'WAVAX', 'WFTM', 'WONE', 'WMOVR', 'WGLMR'
            ],
            'early_altcoins': [
                'LTC', 'NMC', 'PPC', 'XRP', 'DOGE', 'DASH', 'XMR', 'ZEC', 'ETC', 'BCH'
            ]
        }
    
    def get_all_tokens(self) -> List[str]:
        """Get all unique tokens from all categories"""
        all_tokens = set()
        for category, tokens in self.token_categories.items():
            all_tokens.update(tokens)
        return sorted(list(all_tokens))
    
    def collect_from_coingecko(self, symbols: List[str]) -> List[Dict]:
        """Collect data from CoinGecko API"""
        logger.info(f"Collecting data from CoinGecko for {len(symbols)} symbols...")
        collected_data = []
        
        # Get all coins list first to map symbols to IDs
        try:
            response = self.session.get('https://api.coingecko.com/api/v3/coins/list')
            if response.status_code == 200:
                coins_list = response.json()
                symbol_to_id = {coin['symbol'].upper(): coin['id'] for coin in coins_list}
            else:
                logger.warning("Failed to get CoinGecko coins list")
                return collected_data
        except Exception as e:
            logger.error(f"Error getting CoinGecko coins list: {e}")
            return collected_data
        
        time.sleep(self.coingecko_delay)
        
        # Collect market data in batches
        batch_size = 250
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]
            
            try:
                # Get market data
                response = self.session.get('https://api.coingecko.com/api/v3/coins/markets', params={
                    'vs_currency': 'usd',
                    'order': 'market_cap_desc',
                    'per_page': batch_size,
                    'page': i//batch_size + 1,
                    'sparkline': 'false',
                    'price_change_percentage': '24h,7d,30d'
                })
                
                if response.status_code == 200:
                    market_data = response.json()
                    for coin in market_data:
                        symbol = coin.get('symbol', '').upper()
                        if symbol in batch_symbols:
                            collected_data.append({
                                'symbol': symbol,
                                'name': coin.get('name', ''),
                                'price': coin.get('current_price', 0),
                                'market_cap': coin.get('market_cap', 0),
                                'volume_24h': coin.get('total_volume', 0),
                                'change_24h': coin.get('price_change_percentage_24h', 0),
                                'change_7d': coin.get('price_change_percentage_7d_in_currency', 0),
                                'change_30d': coin.get('price_change_percentage_30d_in_currency', 0),
                                'source': 'CoinGecko',
                                'timestamp': datetime.now().isoformat()
                            })
                            logger.info(f"✓ Collected {symbol} from CoinGecko")
                else:
                    logger.warning(f"CoinGecko API request failed with status {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error collecting from CoinGecko batch {i//batch_size + 1}: {e}")
            
            time.sleep(self.coingecko_delay)
        
        logger.info(f"Collected {len(collected_data)} tokens from CoinGecko")
        return collected_data
    
    def collect_from_yahoo_finance(self, symbols: List[str]) -> List[Dict]:
        """Collect data from Yahoo Finance"""
        logger.info(f"Collecting data from Yahoo Finance for {len(symbols)} symbols...")
        collected_data = []
        
        try:
            import yfinance as yf
        except ImportError:
            logger.warning("yfinance not installed, skipping Yahoo Finance collection")
            return collected_data
        
        for symbol in symbols:
            try:
                # Yahoo Finance uses different symbol format for crypto
                yahoo_symbol = f"{symbol}-USD"
                ticker = yf.Ticker(yahoo_symbol)
                
                # Get current info
                info = ticker.info
                if info and 'regularMarketPrice' in info:
                    collected_data.append({
                        'symbol': symbol,
                        'name': info.get('longName', symbol),
                        'price': info.get('regularMarketPrice', 0),
                        'market_cap': info.get('marketCap', 0),
                        'volume_24h': info.get('regularMarketVolume', 0),
                        'change_24h': info.get('regularMarketChangePercent', 0),
                        'source': 'Yahoo Finance',
                        'timestamp': datetime.now().isoformat()
                    })
                    logger.info(f"✓ Collected {symbol} from Yahoo Finance")
                
            except Exception as e:
                logger.debug(f"Failed to collect {symbol} from Yahoo Finance: {e}")
            
            time.sleep(self.yahoo_delay)
        
        logger.info(f"Collected {len(collected_data)} tokens from Yahoo Finance")
        return collected_data

    def collect_from_cryptocompare(self, symbols: List[str]) -> List[Dict]:
        """Collect data from CryptoCompare API"""
        logger.info(f"Collecting data from CryptoCompare for {len(symbols)} symbols...")
        collected_data = []

        # CryptoCompare allows batch requests
        batch_size = 50
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]
            symbols_str = ','.join(batch_symbols)

            try:
                response = self.session.get('https://min-api.cryptocompare.com/data/pricemultifull', params={
                    'fsyms': symbols_str,
                    'tsyms': 'USD'
                })

                if response.status_code == 200:
                    data = response.json()
                    if 'RAW' in data:
                        for symbol in batch_symbols:
                            if symbol in data['RAW'] and 'USD' in data['RAW'][symbol]:
                                coin_data = data['RAW'][symbol]['USD']
                                collected_data.append({
                                    'symbol': symbol,
                                    'name': coin_data.get('FROMSYMBOL', symbol),
                                    'price': coin_data.get('PRICE', 0),
                                    'market_cap': coin_data.get('MKTCAP', 0),
                                    'volume_24h': coin_data.get('VOLUME24HOUR', 0),
                                    'change_24h': coin_data.get('CHANGEPCT24HOUR', 0),
                                    'source': 'CryptoCompare',
                                    'timestamp': datetime.now().isoformat()
                                })
                                logger.info(f"✓ Collected {symbol} from CryptoCompare")
                else:
                    logger.warning(f"CryptoCompare API request failed with status {response.status_code}")

            except Exception as e:
                logger.error(f"Error collecting from CryptoCompare batch {i//batch_size + 1}: {e}")

            time.sleep(self.general_delay)

        logger.info(f"Collected {len(collected_data)} tokens from CryptoCompare")
        return collected_data

    def collect_from_coinmarketcap_scraping(self, symbols: List[str]) -> List[Dict]:
        """Collect data by scraping CoinMarketCap (as fallback)"""
        logger.info(f"Collecting data from CoinMarketCap scraping for {len(symbols)} symbols...")
        collected_data = []

        try:
            from bs4 import BeautifulSoup
        except ImportError:
            logger.warning("BeautifulSoup not installed, skipping CoinMarketCap scraping")
            return collected_data

        for symbol in symbols[:20]:  # Limit to avoid being blocked
            try:
                # Try to find the coin page
                search_url = f"https://coinmarketcap.com/currencies/{symbol.lower()}/"
                response = self.session.get(search_url)

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Try to extract price (this is fragile and may break)
                    price_element = soup.find('div', {'class': 'priceValue'})
                    if price_element:
                        price_text = price_element.get_text().replace('$', '').replace(',', '')
                        try:
                            price = float(price_text)
                            collected_data.append({
                                'symbol': symbol,
                                'name': symbol,
                                'price': price,
                                'source': 'CoinMarketCap Scraping',
                                'timestamp': datetime.now().isoformat()
                            })
                            logger.info(f"✓ Collected {symbol} from CoinMarketCap scraping")
                        except ValueError:
                            pass

            except Exception as e:
                logger.debug(f"Failed to scrape {symbol} from CoinMarketCap: {e}")

            time.sleep(2)  # Be respectful with scraping

        logger.info(f"Collected {len(collected_data)} tokens from CoinMarketCap scraping")
        return collected_data

    def collect_historical_data_cryptocmd(self, symbols: List[str], days: int = 365) -> List[Dict]:
        """Collect historical data using cryptocmd"""
        logger.info(f"Collecting historical data for {len(symbols)} symbols using cryptocmd...")
        collected_data = []

        try:
            from cryptocmd import CmcScraper
        except ImportError:
            logger.warning("cryptocmd not installed, skipping historical collection")
            return collected_data

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        for symbol in symbols[:50]:  # Limit to avoid long execution times
            try:
                scraper = CmcScraper(symbol, start_date.strftime("%d-%m-%Y"), end_date.strftime("%d-%m-%Y"))
                df = scraper.get_dataframe()

                if not df.empty:
                    # Convert to our format
                    for _, row in df.iterrows():
                        collected_data.append({
                            'symbol': symbol,
                            'date': row['Date'],
                            'open': row['Open'],
                            'high': row['High'],
                            'low': row['Low'],
                            'close': row['Close'],
                            'volume': row['Volume'],
                            'market_cap': row['Market Cap'],
                            'source': 'CryptoCMD Historical',
                            'timestamp': datetime.now().isoformat()
                        })

                    logger.info(f"✓ Collected {len(df)} historical records for {symbol}")

            except Exception as e:
                logger.debug(f"Failed to collect historical data for {symbol}: {e}")

            time.sleep(2)  # Rate limiting

        logger.info(f"Collected {len(collected_data)} historical records")
        return collected_data

    def run_comprehensive_collection(self, include_historical: bool = False) -> Tuple[pd.DataFrame, str]:
        """Run comprehensive data collection from all sources"""
        logger.info("Starting comprehensive cryptocurrency data collection...")

        # Get all tokens to collect
        all_tokens = self.get_all_tokens()
        logger.info(f"Total tokens to collect: {len(all_tokens)}")

        # Print token categories
        for category, tokens in self.token_categories.items():
            logger.info(f"{category}: {len(tokens)} tokens")

        all_collected_data = []

        # Collect from multiple sources
        sources = [
            ('CoinGecko', self.collect_from_coingecko),
            ('Yahoo Finance', self.collect_from_yahoo_finance),
            ('CryptoCompare', self.collect_from_cryptocompare),
            ('CoinMarketCap Scraping', self.collect_from_coinmarketcap_scraping)
        ]

        for source_name, collect_func in sources:
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"Collecting from {source_name}...")
                logger.info(f"{'='*60}")

                source_data = collect_func(all_tokens)
                all_collected_data.extend(source_data)

                logger.info(f"✅ {source_name}: Collected {len(source_data)} records")

            except Exception as e:
                logger.error(f"❌ {source_name} collection failed: {e}")

        # Collect historical data if requested
        if include_historical:
            logger.info(f"\n{'='*60}")
            logger.info("Collecting historical data...")
            logger.info(f"{'='*60}")

            historical_data = self.collect_historical_data_cryptocmd(all_tokens[:20])  # Limit for demo
            all_collected_data.extend(historical_data)

            logger.info(f"✅ Historical: Collected {len(historical_data)} records")

        # Convert to DataFrame
        if all_collected_data:
            df = pd.DataFrame(all_collected_data)

            # Save to file
            output_file = f"data/processed/comprehensive_crypto_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(output_file, index=False)

            # Generate summary report
            report = self._generate_collection_report(df, all_tokens)

            logger.info(f"\n{'='*60}")
            logger.info("COLLECTION COMPLETE!")
            logger.info(f"{'='*60}")
            logger.info(f"Total records collected: {len(df)}")
            logger.info(f"Unique symbols: {df['symbol'].nunique()}")
            logger.info(f"Data sources: {df['source'].nunique()}")
            logger.info(f"Output file: {output_file}")

            return df, report
        else:
            logger.warning("No data collected from any source!")
            return pd.DataFrame(), "No data collected"

    def _generate_collection_report(self, df: pd.DataFrame, target_tokens: List[str]) -> str:
        """Generate a comprehensive collection report"""
        report = []
        report.append("COMPREHENSIVE CRYPTOCURRENCY DATA COLLECTION REPORT")
        report.append("=" * 60)
        report.append(f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Target Tokens: {len(target_tokens)}")
        report.append(f"Successfully Collected: {df['symbol'].nunique()}")
        report.append(f"Total Records: {len(df)}")
        report.append(f"Success Rate: {df['symbol'].nunique() / len(target_tokens) * 100:.1f}%")
        report.append("")

        # Data sources breakdown
        report.append("DATA SOURCES:")
        source_counts = df['source'].value_counts()
        for source, count in source_counts.items():
            report.append(f"  {source}: {count} records")
        report.append("")

        # Token categories breakdown
        report.append("TOKEN CATEGORIES COLLECTED:")
        for category, tokens in self.token_categories.items():
            collected_in_category = df[df['symbol'].isin(tokens)]['symbol'].nunique()
            total_in_category = len(tokens)
            percentage = collected_in_category / total_in_category * 100 if total_in_category > 0 else 0
            report.append(f"  {category}: {collected_in_category}/{total_in_category} ({percentage:.1f}%)")
        report.append("")

        # Top tokens by data availability
        report.append("TOP TOKENS BY DATA AVAILABILITY:")
        token_counts = df['symbol'].value_counts().head(20)
        for token, count in token_counts.items():
            report.append(f"  {token}: {count} records")
        report.append("")

        # Missing tokens
        collected_symbols = set(df['symbol'].unique())
        missing_symbols = set(target_tokens) - collected_symbols
        if missing_symbols:
            report.append(f"MISSING TOKENS ({len(missing_symbols)}):")
            missing_list = sorted(list(missing_symbols))
            for i in range(0, len(missing_list), 10):
                batch = missing_list[i:i+10]
                report.append(f"  {', '.join(batch)}")
            report.append("")

        # Data quality metrics
        if 'price' in df.columns:
            valid_prices = df['price'].notna().sum()
            report.append("DATA QUALITY:")
            report.append(f"  Records with valid prices: {valid_prices}/{len(df)} ({valid_prices/len(df)*100:.1f}%)")

            if 'market_cap' in df.columns:
                valid_mcap = df['market_cap'].notna().sum()
                report.append(f"  Records with market cap: {valid_mcap}/{len(df)} ({valid_mcap/len(df)*100:.1f}%)")

            if 'volume_24h' in df.columns:
                valid_volume = df['volume_24h'].notna().sum()
                report.append(f"  Records with volume data: {valid_volume}/{len(df)} ({valid_volume/len(df)*100:.1f}%)")

        report_text = "\n".join(report)

        # Save report to file
        report_file = f"data/processed/collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w') as f:
            f.write(report_text)

        logger.info(f"Collection report saved to: {report_file}")
        return report_text


def main():
    """Main execution function"""
    print("🚀 Comprehensive Cryptocurrency Data Collector")
    print("=" * 60)
    print("This tool will collect data for hundreds of cryptocurrencies from multiple free sources:")
    print("• CoinGecko API (primary source)")
    print("• Yahoo Finance")
    print("• CryptoCompare API")
    print("• CoinMarketCap (scraping fallback)")
    print("• Historical data (optional)")
    print()

    # Initialize collector
    collector = ComprehensiveCryptoCollector()

    # Show what will be collected
    all_tokens = collector.get_all_tokens()
    print(f"📊 Total tokens to collect: {len(all_tokens)}")
    print("\nToken categories:")
    for category, tokens in collector.token_categories.items():
        print(f"  • {category}: {len(tokens)} tokens")

    print(f"\nSample tokens: {', '.join(all_tokens[:20])}...")
    print()

    # Ask user for confirmation
    response = input("Do you want to proceed with comprehensive data collection? (y/n): ").lower().strip()
    if response != 'y':
        print("Collection cancelled.")
        return

    # Ask about historical data
    historical_response = input("Include historical data collection? (slower, y/n): ").lower().strip()
    include_historical = historical_response == 'y'

    print("\n🔄 Starting comprehensive data collection...")
    print("This may take several minutes depending on API rate limits...")
    print()

    try:
        # Run collection
        df, report = collector.run_comprehensive_collection(include_historical=include_historical)

        if not df.empty:
            print("\n✅ COLLECTION SUCCESSFUL!")
            print("=" * 60)
            print(report)
            print("\n📁 Files created:")
            print(f"  • Data: data/processed/comprehensive_crypto_data_*.csv")
            print(f"  • Report: data/processed/collection_report_*.txt")
            print(f"  • Logs: logs/comprehensive_collector.log")
            print("\n🎉 You now have comprehensive cryptocurrency data!")
        else:
            print("\n❌ COLLECTION FAILED!")
            print("No data was collected. Check the logs for details.")

    except KeyboardInterrupt:
        print("\n⏹️  Collection interrupted by user.")
    except Exception as e:
        logger.error(f"Collection failed with error: {e}")
        print(f"\n❌ Collection failed: {e}")
        print("Check the logs for more details.")


if __name__ == "__main__":
    main()
