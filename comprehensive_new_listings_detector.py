"""
Comprehensive New Listings Detector for Time-Series Cryptocurrency Data
Analyzes historical price data to identify when cryptocurrencies first appeared
"""
import os
import pandas as pd
from datetime import datetime, timedelta
import logging
from tqdm import tqdm
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/comprehensive_detector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveNewListingsDetector:
    def __init__(self):
        self.processed_dir = PROCESSED_DIR
        os.makedirs(self.processed_dir, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def load_all_historical_data(self):
        """Load and combine all available historical data sources"""
        logger.info("Loading all historical data sources...")
        
        data_files = [
            'yahoo_finance_crypto_data.csv',
            'cryptocompare_historical_data.csv',
            'full_historical_data.csv'  # From sample data
        ]
        
        all_datasets = []
        
        for filename in data_files:
            filepath = os.path.join(self.processed_dir, filename)
            if os.path.exists(filepath):
                try:
                    df = pd.read_csv(filepath)
                    logger.info(f"Loaded {filename}: {len(df)} records")
                    all_datasets.append(df)
                except Exception as e:
                    logger.warning(f"Failed to load {filename}: {str(e)}")
        
        if not all_datasets:
            logger.error("No historical data files found")
            return pd.DataFrame()
        
        # Combine all datasets
        combined_df = pd.concat(all_datasets, ignore_index=True, sort=False)
        logger.info(f"Combined dataset: {len(combined_df)} total records")
        
        return combined_df
    
    def standardize_data_format(self, df):
        """Standardize different data formats into a common structure"""
        logger.info("Standardizing data format...")
        
        # Create a copy to avoid modifying original
        standardized_df = df.copy()
        
        # Standardize date column
        date_columns = ['Date', 'date', 'snapshot_date', 'time']
        date_col = None
        
        for col in date_columns:
            if col in standardized_df.columns:
                date_col = col
                break
        
        if date_col:
            standardized_df['Date'] = pd.to_datetime(standardized_df[date_col], errors='coerce', utc=True)
            # Convert to timezone-naive for easier comparison
            standardized_df['Date'] = standardized_df['Date'].dt.tz_localize(None)
        else:
            logger.error("No date column found in data")
            return pd.DataFrame()
        
        # Standardize symbol column
        symbol_columns = ['Symbol', 'symbol', 'fsym']
        symbol_col = None
        
        for col in symbol_columns:
            if col in standardized_df.columns:
                symbol_col = col
                break
        
        if symbol_col:
            standardized_df['Symbol'] = standardized_df[symbol_col].astype(str).str.upper().str.strip()
        else:
            logger.error("No symbol column found in data")
            return pd.DataFrame()
        
        # Standardize price columns
        price_columns = ['Close', 'close', 'Price', 'price']
        for col in price_columns:
            if col in standardized_df.columns:
                # Clean price data (remove $ and commas)
                if standardized_df[col].dtype == 'object':
                    standardized_df[col] = standardized_df[col].astype(str).str.replace('$', '').str.replace(',', '')
                standardized_df['Price'] = pd.to_numeric(standardized_df[col], errors='coerce')
                break
        
        # Standardize market cap if available
        mcap_columns = ['Market_Cap', 'market_cap', 'Market Cap']
        for col in mcap_columns:
            if col in standardized_df.columns:
                if standardized_df[col].dtype == 'object':
                    standardized_df[col] = standardized_df[col].astype(str).str.replace('$', '').str.replace(',', '')
                standardized_df['Market_Cap'] = pd.to_numeric(standardized_df[col], errors='coerce')
                break
        
        # Add source information if not present
        if 'Source' not in standardized_df.columns:
            standardized_df['Source'] = 'Unknown'
        
        # Filter out invalid data
        standardized_df = standardized_df.dropna(subset=['Date', 'Symbol', 'Price'])
        standardized_df = standardized_df[standardized_df['Symbol'] != 'NAN']
        standardized_df = standardized_df[standardized_df['Price'] > 0]
        
        logger.info(f"Standardized data: {len(standardized_df)} valid records")
        return standardized_df
    
    def detect_first_appearances(self, df):
        """Detect when each cryptocurrency first appeared in the data"""
        logger.info("Detecting first appearances of cryptocurrencies...")
        
        # Group by symbol and find first date
        first_appearances = df.groupby('Symbol').agg({
            'Date': 'min',
            'Price': 'first',
            'Market_Cap': 'first',
            'Source': 'first'
        }).reset_index()
        
        # Rename columns for clarity
        first_appearances = first_appearances.rename(columns={
            'Date': 'first_seen_date',
            'Price': 'first_seen_price',
            'Market_Cap': 'first_seen_market_cap',
            'Source': 'data_source'
        })
        
        # Sort by first seen date
        first_appearances = first_appearances.sort_values('first_seen_date')
        
        logger.info(f"Detected {len(first_appearances)} unique cryptocurrencies")
        return first_appearances
    
    def identify_new_listings_by_period(self, first_appearances_df):
        """Identify new listings by time periods"""
        logger.info("Categorizing new listings by time periods...")
        
        # Define time periods (timezone-naive)
        periods = {
            '2020': (datetime(2020, 1, 1), datetime(2020, 12, 31)),
            '2021': (datetime(2021, 1, 1), datetime(2021, 12, 31)),
            '2022': (datetime(2022, 1, 1), datetime(2022, 12, 31)),
            '2023': (datetime(2023, 1, 1), datetime(2023, 12, 31)),
            '2024': (datetime(2024, 1, 1), datetime(2024, 12, 31))
        }
        
        period_analysis = {}
        
        for period_name, (start_date, end_date) in periods.items():
            period_listings = first_appearances_df[
                (first_appearances_df['first_seen_date'] >= start_date) &
                (first_appearances_df['first_seen_date'] <= end_date)
            ]
            
            period_analysis[period_name] = {
                'count': len(period_listings),
                'listings': period_listings.copy()
            }
            
            logger.info(f"{period_name}: {len(period_listings)} new listings")
        
        return period_analysis
    
    def generate_comprehensive_analysis(self, first_appearances_df, period_analysis):
        """Generate comprehensive analysis of new listings"""
        logger.info("Generating comprehensive analysis...")
        
        analysis_lines = [
            "Comprehensive Cryptocurrency New Listings Analysis",
            "=" * 60,
            f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Unique Cryptocurrencies: {len(first_appearances_df)}",
            f"Date Range: {first_appearances_df['first_seen_date'].min()} to {first_appearances_df['first_seen_date'].max()}",
            ""
        ]
        
        # Period breakdown
        analysis_lines.extend([
            "New Listings by Year:",
            "-" * 30
        ])
        
        for year, data in period_analysis.items():
            analysis_lines.append(f"{year}: {data['count']} new listings")
        
        analysis_lines.append("")
        
        # Data source breakdown
        if 'data_source' in first_appearances_df.columns:
            source_counts = first_appearances_df['data_source'].value_counts()
            analysis_lines.extend([
                "Listings by Data Source:",
                "-" * 30,
                *[f"{source}: {count}" for source, count in source_counts.items()],
                ""
            ])
        
        # Top new listings by market cap (if available)
        if 'first_seen_market_cap' in first_appearances_df.columns:
            top_by_mcap = first_appearances_df.nlargest(20, 'first_seen_market_cap')
            analysis_lines.extend([
                "Top 20 New Listings by Initial Market Cap:",
                "-" * 50
            ])
            
            for _, row in top_by_mcap.iterrows():
                mcap_str = f"${row['first_seen_market_cap']:,.0f}" if pd.notna(row['first_seen_market_cap']) else "N/A"
                price_str = f"${row['first_seen_price']:.6f}" if pd.notna(row['first_seen_price']) else "N/A"
                analysis_lines.append(
                    f"{row['Symbol']:8s} - {row['first_seen_date'].strftime('%Y-%m-%d')} - "
                    f"Price: {price_str:>12s} - Market Cap: {mcap_str:>15s}"
                )
            
            analysis_lines.append("")
        
        # Recent new listings (last 2 years)
        recent_cutoff = datetime.now() - timedelta(days=730)
        recent_listings = first_appearances_df[
            first_appearances_df['first_seen_date'] >= recent_cutoff
        ].head(50)
        
        if not recent_listings.empty:
            analysis_lines.extend([
                "Recent New Listings (Last 2 Years):",
                "-" * 40
            ])
            
            for _, row in recent_listings.iterrows():
                price_str = f"${row['first_seen_price']:.6f}" if pd.notna(row['first_seen_price']) else "N/A"
                analysis_lines.append(
                    f"{row['Symbol']:8s} - {row['first_seen_date'].strftime('%Y-%m-%d')} - "
                    f"Price: {price_str:>12s} - Source: {row['data_source']}"
                )
        
        analysis_text = "\n".join(analysis_lines)
        
        # Save analysis
        analysis_file = f"{self.processed_dir}/comprehensive_new_listings_analysis.txt"
        with open(analysis_file, 'w') as f:
            f.write(analysis_text)
        
        logger.info(f"Analysis saved to {analysis_file}")
        return analysis_text
    
    def run_comprehensive_detection(self):
        """Run the complete comprehensive new listings detection"""
        logger.info("Starting comprehensive new listings detection...")
        
        # Load all data
        combined_data = self.load_all_historical_data()
        if combined_data.empty:
            logger.error("No data available for analysis")
            return None, None
        
        # Standardize format
        standardized_data = self.standardize_data_format(combined_data)
        if standardized_data.empty:
            logger.error("Data standardization failed")
            return None, None
        
        # Detect first appearances
        first_appearances = self.detect_first_appearances(standardized_data)
        
        # Analyze by periods
        period_analysis = self.identify_new_listings_by_period(first_appearances)
        
        # Save results
        output_file = f"{self.processed_dir}/comprehensive_new_listings.csv"
        first_appearances.to_csv(output_file, index=False)
        logger.info(f"Saved comprehensive new listings to {output_file}")
        
        # Generate analysis
        analysis = self.generate_comprehensive_analysis(first_appearances, period_analysis)
        
        return first_appearances, analysis

if __name__ == "__main__":
    detector = ComprehensiveNewListingsDetector()
    
    print("Starting comprehensive new listings detection...")
    print("This will analyze all available historical data sources.")
    print()
    
    first_appearances, analysis = detector.run_comprehensive_detection()
    
    if first_appearances is not None:
        print("\n" + "="*60)
        print("COMPREHENSIVE ANALYSIS COMPLETE!")
        print("="*60)
        print(analysis)
    else:
        print("Analysis failed. Check logs for details.")
