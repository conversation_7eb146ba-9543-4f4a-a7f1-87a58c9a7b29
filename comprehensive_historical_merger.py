"""
Comprehensive Historical Data Merger
Combines existing data (2020-2024) with extended historical data (2011-2024)
"""

import os
import pandas as pd
import logging
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveHistoricalMerger:
    def __init__(self):
        self.data_dir = "data"
        self.processed_dir = f"{self.data_dir}/processed"
        self.output_dir = f"{self.data_dir}/comprehensive"
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Date ranges
        self.extended_start = datetime(2011, 1, 1)
        self.current_start = datetime(2020, 10, 1)
        self.end_date = datetime(2024, 10, 3)
    
    def load_existing_datasets(self):
        """Load all existing processed datasets"""
        logger.info("Loading existing processed datasets...")
        
        existing_files = [
            'full_historical_data.csv',
            'yahoo_finance_crypto_data.csv',
            'cryptocompare_historical_data.csv',
            'comprehensive_token_dataset.csv',
            'multi_chain_tokens_data.csv',
            'discovered_tokens_data.csv',
            'free_pulsechain_hex_current_data.csv'
        ]
        
        existing_datasets = []
        
        for filename in existing_files:
            filepath = os.path.join(self.processed_dir, filename)
            if os.path.exists(filepath):
                try:
                    df = pd.read_csv(filepath)
                    df['dataset_source'] = filename.replace('.csv', '')
                    existing_datasets.append(df)
                    logger.info(f"Loaded {filename}: {len(df)} records")
                except Exception as e:
                    logger.warning(f"Failed to load {filename}: {str(e)}")
        
        if existing_datasets:
            combined_existing = pd.concat(existing_datasets, ignore_index=True)
            logger.info(f"Combined existing datasets: {len(combined_existing)} total records")
            return combined_existing
        
        return pd.DataFrame()
    
    def load_extended_datasets(self):
        """Load all extended historical datasets"""
        logger.info("Loading extended historical datasets...")
        
        extended_files = [
            'extended_historical_combined_data.csv',
            'bitcoincharts_historical_data.csv',
            'early_altcoins_historical_data.csv',
            'kaggle_bitcoin_historical_data.csv',
            'github_datasets_historical_data.csv',
            'wayback_cmc_historical_data.csv',
            'blockchain_info_historical_data.csv'
        ]
        
        extended_datasets = []
        
        for filename in extended_files:
            filepath = os.path.join(self.processed_dir, filename)
            if os.path.exists(filepath):
                try:
                    df = pd.read_csv(filepath)
                    df['dataset_source'] = filename.replace('.csv', '')
                    extended_datasets.append(df)
                    logger.info(f"Loaded {filename}: {len(df)} records")
                except Exception as e:
                    logger.warning(f"Failed to load {filename}: {str(e)}")
        
        if extended_datasets:
            combined_extended = pd.concat(extended_datasets, ignore_index=True)
            logger.info(f"Combined extended datasets: {len(combined_extended)} total records")
            return combined_extended
        
        return pd.DataFrame()
    
    def standardize_columns(self, df, dataset_type="unknown"):
        """Standardize column names and formats across datasets"""
        logger.info(f"Standardizing columns for {dataset_type} dataset...")
        
        # Common column mappings
        column_mappings = {
            # Date columns
            'Date': 'date',
            'Timestamp': 'date',
            'timestamp': 'date',
            'time': 'date',
            'snapshot_date': 'date',
            
            # Symbol columns
            'Symbol': 'symbol',
            'Coin': 'symbol',
            'Currency': 'symbol',
            'Asset': 'symbol',
            
            # Name columns
            'Name': 'name',
            'CoinName': 'name',
            'Currency Name': 'name',
            
            # Price columns
            'Price': 'price',
            'Close': 'price',
            'close': 'price',
            'Last': 'price',
            'market-price': 'price',
            
            # Market cap columns
            'Market Cap': 'market_cap',
            'MarketCap': 'market_cap',
            'Market_Cap': 'market_cap',
            'market-cap': 'market_cap',
            
            # Volume columns
            'Volume': 'volume',
            'Volume 24h': 'volume_24h',
            'trade-volume': 'volume',
            
            # Rank columns
            'Rank': 'rank',
            'CMC_Rank': 'rank',
            'Market Cap Rank': 'rank'
        }
        
        # Apply column mappings
        df = df.rename(columns=column_mappings)
        
        # Standardize date column
        if 'date' in df.columns:
            try:
                df['date'] = pd.to_datetime(df['date'], errors='coerce')
            except Exception as e:
                logger.warning(f"Failed to convert date column: {str(e)}")
        
        # Ensure numeric columns are numeric
        numeric_columns = ['price', 'market_cap', 'volume', 'volume_24h', 'rank']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Add metadata columns if missing
        if 'symbol' not in df.columns and 'Symbol' in df.columns:
            df['symbol'] = df['Symbol']
        
        if 'source' not in df.columns:
            df['source'] = dataset_type
        
        return df
    
    def deduplicate_records(self, df):
        """Remove duplicate records based on date, symbol, and source"""
        logger.info("Removing duplicate records...")
        
        initial_count = len(df)
        
        # Define columns for deduplication
        dedup_columns = ['date', 'symbol']
        if 'source' in df.columns:
            dedup_columns.append('source')
        
        # Remove duplicates, keeping the first occurrence
        df_deduped = df.drop_duplicates(subset=dedup_columns, keep='first')
        
        removed_count = initial_count - len(df_deduped)
        logger.info(f"Removed {removed_count} duplicate records ({removed_count/initial_count*100:.1f}%)")
        
        return df_deduped
    
    def merge_datasets(self, existing_df, extended_df):
        """Merge existing and extended datasets with overlap handling"""
        logger.info("Merging existing and extended datasets...")
        
        # Standardize both datasets
        existing_standardized = self.standardize_columns(existing_df, "existing")
        extended_standardized = self.standardize_columns(extended_df, "extended")
        
        # Combine datasets
        combined_df = pd.concat([existing_standardized, extended_standardized], ignore_index=True)
        
        # Remove duplicates
        merged_df = self.deduplicate_records(combined_df)
        
        # Sort by date and symbol
        if 'date' in merged_df.columns:
            merged_df = merged_df.sort_values(['date', 'symbol'], na_position='last')
        
        logger.info(f"Merged dataset contains {len(merged_df)} records")
        
        return merged_df
    
    def generate_comprehensive_analysis(self, merged_df):
        """Generate comprehensive analysis of the merged dataset"""
        logger.info("Generating comprehensive analysis...")
        
        analysis_lines = [
            "COMPREHENSIVE HISTORICAL CRYPTOCURRENCY DATA ANALYSIS",
            "=" * 60,
            f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Records: {len(merged_df):,}",
            ""
        ]
        
        if 'date' in merged_df.columns and not merged_df['date'].isna().all():
            min_date = merged_df['date'].min()
            max_date = merged_df['date'].max()
            date_range_years = (max_date - min_date).days / 365.25
            
            analysis_lines.extend([
                "DATE COVERAGE:",
                "-" * 20,
                f"Earliest Date: {min_date.strftime('%Y-%m-%d') if pd.notna(min_date) else 'N/A'}",
                f"Latest Date: {max_date.strftime('%Y-%m-%d') if pd.notna(max_date) else 'N/A'}",
                f"Total Years: {date_range_years:.1f} years",
                ""
            ])
        
        if 'symbol' in merged_df.columns:
            unique_symbols = merged_df['symbol'].nunique()
            top_symbols = merged_df['symbol'].value_counts().head(10)
            
            analysis_lines.extend([
                "CRYPTOCURRENCY COVERAGE:",
                "-" * 25,
                f"Unique Cryptocurrencies: {unique_symbols:,}",
                "",
                "Top 10 Most Tracked Cryptocurrencies:",
            ])
            
            for symbol, count in top_symbols.items():
                analysis_lines.append(f"  {symbol}: {count:,} records")
            
            analysis_lines.append("")
        
        if 'source' in merged_df.columns:
            source_counts = merged_df['source'].value_counts()
            
            analysis_lines.extend([
                "DATA SOURCES:",
                "-" * 15
            ])
            
            for source, count in source_counts.items():
                percentage = (count / len(merged_df)) * 100
                analysis_lines.append(f"  {source}: {count:,} records ({percentage:.1f}%)")
            
            analysis_lines.append("")
        
        # Historical periods analysis
        if 'date' in merged_df.columns and not merged_df['date'].isna().all():
            periods = {
                'Early Era (2011-2013)': (datetime(2011, 1, 1), datetime(2013, 12, 31)),
                'Growth Era (2014-2017)': (datetime(2014, 1, 1), datetime(2017, 12, 31)),
                'Boom Era (2018-2020)': (datetime(2018, 1, 1), datetime(2020, 12, 31)),
                'Modern Era (2021-2024)': (datetime(2021, 1, 1), datetime(2024, 12, 31))
            }
            
            analysis_lines.extend([
                "HISTORICAL PERIODS COVERAGE:",
                "-" * 30
            ])
            
            for period_name, (start_date, end_date) in periods.items():
                period_data = merged_df[
                    (merged_df['date'] >= start_date) & 
                    (merged_df['date'] <= end_date)
                ]
                
                if not period_data.empty:
                    unique_cryptos = period_data['symbol'].nunique() if 'symbol' in period_data.columns else 0
                    analysis_lines.append(f"  {period_name}: {len(period_data):,} records, {unique_cryptos} unique cryptos")
                else:
                    analysis_lines.append(f"  {period_name}: No data")
        
        analysis_text = "\n".join(analysis_lines)
        
        # Save analysis
        analysis_file = f"{self.output_dir}/comprehensive_historical_analysis.txt"
        with open(analysis_file, 'w') as f:
            f.write(analysis_text)
        
        return analysis_text
    
    def create_comprehensive_dataset(self):
        """Create the comprehensive 12-13 year historical dataset"""
        logger.info("Creating comprehensive historical dataset...")
        
        # Load datasets
        existing_df = self.load_existing_datasets()
        extended_df = self.load_extended_datasets()
        
        if existing_df.empty and extended_df.empty:
            logger.error("No datasets found to merge!")
            return None, "No datasets available for merging"
        
        # Merge datasets
        if not existing_df.empty and not extended_df.empty:
            merged_df = self.merge_datasets(existing_df, extended_df)
        elif not existing_df.empty:
            merged_df = self.standardize_columns(existing_df, "existing_only")
        else:
            merged_df = self.standardize_columns(extended_df, "extended_only")
        
        # Save comprehensive dataset
        output_file = f"{self.output_dir}/comprehensive_12_year_crypto_dataset.csv"
        merged_df.to_csv(output_file, index=False)
        logger.info(f"Saved comprehensive dataset: {len(merged_df)} records to {output_file}")
        
        # Generate analysis
        analysis = self.generate_comprehensive_analysis(merged_df)
        
        return merged_df, analysis


if __name__ == "__main__":
    merger = ComprehensiveHistoricalMerger()
    
    print("Creating Comprehensive 12-13 Year Historical Cryptocurrency Dataset")
    print("=" * 70)
    print("This will merge:")
    print("- Existing data (2020-2024)")
    print("- Extended historical data (2011-2024)")
    print("- Remove duplicates and standardize formats")
    print()
    
    dataset, analysis = merger.create_comprehensive_dataset()
    
    if dataset is not None:
        print("\n" + "=" * 70)
        print("COMPREHENSIVE DATASET CREATED!")
        print("=" * 70)
        print(analysis)
    else:
        print("\n" + "=" * 70)
        print("FAILED TO CREATE DATASET")
        print("=" * 70)
        print(analysis)
