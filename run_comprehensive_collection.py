#!/usr/bin/env python3
"""
Simple script to run comprehensive cryptocurrency data collection
This script makes it easy to collect data for 166+ cryptocurrencies
"""

import os
import sys
import subprocess
from pathlib import Path

def check_virtual_environment():
    """Check if we're in the correct virtual environment"""
    venv_path = Path("crypto_env")
    if not venv_path.exists():
        print("❌ Virtual environment 'crypto_env' not found!")
        print("Please make sure you're in the correct directory.")
        return False
    
    # Check if we're in the virtual environment
    if "crypto_env" not in sys.executable:
        print("⚠️  Virtual environment not activated!")
        print("Please run: source crypto_env/bin/activate")
        return False
    
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import pandas
        import requests
        import yfinance
        import cryptocmd
        import tqdm
        from comprehensive_crypto_collector import ComprehensiveCryptoCollector
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install requirements: pip install -r requirements_comprehensive.txt")
        return False

def run_collection():
    """Run the comprehensive collection"""
    print("🚀 Starting Comprehensive Cryptocurrency Data Collection")
    print("=" * 60)
    
    # Check environment
    if not check_virtual_environment():
        return False
    
    if not check_dependencies():
        return False
    
    # Import and run collector
    try:
        from comprehensive_crypto_collector import ComprehensiveCryptoCollector
        
        # Initialize collector
        collector = ComprehensiveCryptoCollector()
        
        # Show what will be collected
        all_tokens = collector.get_all_tokens()
        print(f"📊 Ready to collect data for {len(all_tokens)} cryptocurrencies")
        print("\nToken categories:")
        for category, tokens in collector.token_categories.items():
            print(f"  • {category}: {len(tokens)} tokens")
        
        print(f"\nSample tokens: {', '.join(all_tokens[:15])}...")
        
        # Ask for confirmation
        response = input("\nProceed with collection? (y/n): ").lower().strip()
        if response != 'y':
            print("Collection cancelled.")
            return False
        
        # Ask about historical data
        historical = input("Include historical data? (slower, y/n): ").lower().strip() == 'y'
        
        print("\n🔄 Running comprehensive collection...")
        print("This may take several minutes...")
        
        # Run collection
        df, report = collector.run_comprehensive_collection(include_historical=historical)
        
        if not df.empty:
            print("\n✅ COLLECTION SUCCESSFUL!")
            print("=" * 60)
            print(f"📊 Collected {len(df)} records")
            print(f"🎯 Unique tokens: {df['symbol'].nunique()}")
            print(f"📡 Data sources: {df['source'].nunique()}")
            
            # Show top tokens
            if 'market_cap' in df.columns:
                top_tokens = df.groupby('symbol')['market_cap'].max().sort_values(ascending=False).head(10)
                print(f"\n🏆 Top 10 tokens by market cap:")
                for symbol, mcap in top_tokens.items():
                    if mcap > 0:
                        print(f"  {symbol}: ${mcap:,.0f}")
            
            print(f"\n📁 Files created:")
            print(f"  • Data: data/processed/comprehensive_crypto_data_*.csv")
            print(f"  • Report: data/processed/collection_report_*.txt")
            print(f"  • Logs: logs/comprehensive_collector.log")
            
            print(f"\n🎉 SUCCESS! You now have comprehensive data for {df['symbol'].nunique()} cryptocurrencies!")
            print("   (Previously you only had 19 tokens)")
            
            return True
        else:
            print("\n❌ COLLECTION FAILED!")
            print("No data was collected. Check logs/comprehensive_collector.log for details.")
            return False
            
    except Exception as e:
        print(f"\n❌ Error during collection: {e}")
        print("Check logs/comprehensive_collector.log for more details.")
        return False

def main():
    """Main function"""
    print("🔍 Comprehensive Cryptocurrency Data Collector")
    print("Fixes the issue where only 19 tokens were collected")
    print("Now collects 166+ cryptocurrencies from multiple sources!")
    print()
    
    success = run_collection()
    
    if success:
        print("\n📖 Next steps:")
        print("1. Check the generated CSV file in data/processed/")
        print("2. Read the collection report for detailed statistics")
        print("3. Use the data for your analysis!")
        print("\n💡 Tip: Run this script regularly to keep your data updated")
    else:
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the virtual environment: source crypto_env/bin/activate")
        print("2. Check your internet connection")
        print("3. Look at logs/comprehensive_collector.log for details")
        print("4. Try running again in a few minutes (API rate limits)")

if __name__ == "__main__":
    main()
