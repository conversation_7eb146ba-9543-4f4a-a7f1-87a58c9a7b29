#!/usr/bin/env python3
"""
Master script to run the complete extended historical cryptocurrency data collection
Collects 12-13 years of data from multiple sources (2011-2024)
"""

import os
import sys
import argparse
import logging
from datetime import datetime
import pandas as pd

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from extended_historical_collector import ExtendedHistoricalCollector
from comprehensive_historical_merger import ComprehensiveHistoricalMerger
from extended_config import *

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(f"{EXTENDED_DATA_PATHS['logs_dir']}/extended_collection.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ExtendedCollectionOrchestrator:
    def __init__(self):
        self.collector = ExtendedHistoricalCollector()
        self.merger = ComprehensiveHistoricalMerger()
        
        # Create all necessary directories
        for path in EXTENDED_DATA_PATHS.values():
            os.makedirs(path, exist_ok=True)
        
        self.start_time = datetime.now()
        
    def run_extended_collection(self):
        """Run the complete extended historical data collection"""
        logger.info("Starting Extended Historical Cryptocurrency Data Collection")
        logger.info("=" * 70)
        logger.info(f"Date Range: {EXTENDED_START_DATE.strftime('%Y-%m-%d')} to {END_DATE.strftime('%Y-%m-%d')}")
        logger.info(f"Total Years: {(END_DATE - EXTENDED_START_DATE).days / 365.25:.1f} years")
        logger.info("")
        
        try:
            # Step 1: Collect extended historical data
            logger.info("STEP 1: Collecting Extended Historical Data")
            logger.info("-" * 50)
            
            extended_data, extended_summary = self.collector.collect_all_extended_sources()
            
            if extended_data.empty:
                logger.warning("No extended historical data collected!")
                return False, "No extended historical data collected"
            
            logger.info(f"✓ Extended collection complete: {len(extended_data)} records")
            
            # Step 2: Merge with existing data
            logger.info("\nSTEP 2: Merging with Existing Data")
            logger.info("-" * 40)
            
            comprehensive_data, comprehensive_analysis = self.merger.create_comprehensive_dataset()
            
            if comprehensive_data is None:
                logger.error("Failed to create comprehensive dataset!")
                return False, "Failed to merge datasets"
            
            logger.info(f"✓ Comprehensive dataset created: {len(comprehensive_data)} records")
            
            # Step 3: Generate final report
            logger.info("\nSTEP 3: Generating Final Report")
            logger.info("-" * 35)
            
            final_report = self._generate_final_report(extended_summary, comprehensive_analysis)
            
            logger.info("✓ Final report generated")
            
            return True, final_report
            
        except Exception as e:
            logger.error(f"Extended collection failed: {str(e)}")
            return False, f"Collection failed: {str(e)}"
    
    def run_specific_source(self, source_name):
        """Run collection for a specific data source"""
        logger.info(f"Running collection for specific source: {source_name}")
        
        source_methods = {
            'bitcoincharts': self.collector.collect_bitcoincharts_data,
            'early_altcoins': self.collector.collect_early_altcoin_data,
            'kaggle': self.collector.collect_kaggle_bitcoin_historical,
            'github': self.collector.collect_github_datasets,
            'wayback': self.collector.collect_wayback_machine_cmc_data,
            'blockchain_info': self.collector.collect_blockchain_info_data
        }
        
        if source_name not in source_methods:
            logger.error(f"Unknown source: {source_name}")
            logger.info(f"Available sources: {list(source_methods.keys())}")
            return False, f"Unknown source: {source_name}"
        
        try:
            method = source_methods[source_name]
            data = method()
            
            if not data.empty:
                logger.info(f"✓ {source_name}: Collected {len(data)} records")
                return True, f"{source_name}: {len(data)} records collected"
            else:
                logger.warning(f"✗ {source_name}: No data collected")
                return False, f"{source_name}: No data collected"
                
        except Exception as e:
            logger.error(f"Failed to collect from {source_name}: {str(e)}")
            return False, f"{source_name}: {str(e)}"
    
    def validate_extended_data(self):
        """Validate the extended historical dataset"""
        logger.info("Validating extended historical dataset...")
        
        validation_results = []
        
        # Check for required files
        required_files = [
            EXTENDED_OUTPUT_FILES['extended_combined'],
            EXTENDED_OUTPUT_FILES['comprehensive_dataset']
        ]
        
        for filename in required_files:
            filepath = os.path.join(EXTENDED_DATA_PATHS['processed_dir'], filename)
            
            if os.path.exists(filepath):
                try:
                    df = pd.read_csv(filepath)
                    
                    # Basic validation
                    record_count = len(df)
                    date_range = "N/A"
                    unique_symbols = "N/A"
                    
                    if 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'], errors='coerce')
                        min_date = df['date'].min()
                        max_date = df['date'].max()
                        if pd.notna(min_date) and pd.notna(max_date):
                            date_range = f"{min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}"
                    
                    if 'symbol' in df.columns:
                        unique_symbols = df['symbol'].nunique()
                    
                    validation_results.append({
                        'file': filename,
                        'status': 'Valid',
                        'records': record_count,
                        'date_range': date_range,
                        'unique_symbols': unique_symbols
                    })
                    
                    logger.info(f"✓ {filename}: {record_count} records, {unique_symbols} symbols")
                    
                except Exception as e:
                    validation_results.append({
                        'file': filename,
                        'status': f'Error: {str(e)}',
                        'records': 0,
                        'date_range': 'N/A',
                        'unique_symbols': 'N/A'
                    })
                    logger.error(f"✗ {filename}: {str(e)}")
            else:
                validation_results.append({
                    'file': filename,
                    'status': 'Missing',
                    'records': 0,
                    'date_range': 'N/A',
                    'unique_symbols': 'N/A'
                })
                logger.warning(f"✗ {filename}: File not found")
        
        return validation_results
    
    def _generate_final_report(self, extended_summary, comprehensive_analysis):
        """Generate the final comprehensive report"""
        
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        report_lines = [
            "EXTENDED HISTORICAL CRYPTOCURRENCY DATA COLLECTION",
            "FINAL COMPREHENSIVE REPORT",
            "=" * 70,
            f"Collection Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"Collection Completed: {end_time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Duration: {duration}",
            f"Target Date Range: {EXTENDED_START_DATE.strftime('%Y-%m-%d')} to {END_DATE.strftime('%Y-%m-%d')}",
            f"Total Years Targeted: {(END_DATE - EXTENDED_START_DATE).days / 365.25:.1f} years",
            "",
            "EXTENDED COLLECTION SUMMARY:",
            "-" * 40,
            extended_summary,
            "",
            "COMPREHENSIVE DATASET ANALYSIS:",
            "-" * 45,
            comprehensive_analysis,
            "",
            "DATA VALIDATION RESULTS:",
            "-" * 30
        ]
        
        # Add validation results
        validation_results = self.validate_extended_data()
        for result in validation_results:
            report_lines.append(
                f"  {result['file']}: {result['status']} "
                f"({result['records']} records, {result['unique_symbols']} symbols)"
            )
        
        report_lines.extend([
            "",
            "COLLECTION SUCCESS METRICS:",
            "-" * 35,
            f"✓ Extended historical sources attempted: 6",
            f"✓ Data merger completed successfully",
            f"✓ Comprehensive dataset created",
            f"✓ Analysis and validation completed",
            "",
            "NEXT STEPS:",
            "-" * 15,
            "1. Review the comprehensive dataset in data/comprehensive/",
            "2. Use the data for your 12-13 year historical analysis",
            "3. Consider running additional validation if needed",
            "4. Explore the data using your preferred analysis tools",
            "",
            "FILES CREATED:",
            "-" * 15
        ])
        
        # List created files
        output_dir = EXTENDED_DATA_PATHS['comprehensive_dir']
        if os.path.exists(output_dir):
            for file in os.listdir(output_dir):
                filepath = os.path.join(output_dir, file)
                if os.path.isfile(filepath):
                    size_mb = os.path.getsize(filepath) / (1024 * 1024)
                    report_lines.append(f"  {file} ({size_mb:.1f} MB)")
        
        report_text = "\n".join(report_lines)
        
        # Save final report
        report_file = os.path.join(
            EXTENDED_DATA_PATHS['comprehensive_dir'], 
            'final_extended_collection_report.txt'
        )
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        return report_text


def main():
    parser = argparse.ArgumentParser(
        description='Extended Historical Cryptocurrency Data Collection (12-13 years)'
    )
    parser.add_argument(
        '--source', 
        choices=['bitcoincharts', 'early_altcoins', 'kaggle', 'github', 'wayback', 'blockchain_info', 'cryptodatadownload', 'github_bulk_downloader'],
        help='Collect from specific source only'
    )
    parser.add_argument(
        '--validate-only', 
        action='store_true',
        help='Only validate existing extended data'
    )
    parser.add_argument(
        '--merge-only',
        action='store_true', 
        help='Only merge existing datasets'
    )
    
    args = parser.parse_args()
    
    orchestrator = ExtendedCollectionOrchestrator()
    
    print("Extended Historical Cryptocurrency Data Collection")
    print("=" * 60)
    print(f"Target: {(END_DATE - EXTENDED_START_DATE).days / 365.25:.1f} years of data ({EXTENDED_START_DATE.year}-{END_DATE.year})")
    print("- Wayback Machine CoinMarketCap archives")
    print("- Blockchain.info historical charts")
    print("- CryptoDataDownload (Bulk daily data)")
    print("- GitHub Bulk Downloader (Script for 1900+ tokens)")
    print()
    
    if args.validate_only:
        print("Running validation only...")
        results = orchestrator.validate_extended_data()
        for result in results:
            print(f"  {result['file']}: {result['status']}")
    
    elif args.merge_only:
        print("Running merge only...")
        data, analysis = orchestrator.merger.create_comprehensive_dataset()
        if data is not None:
            print(f"✓ Merge successful: {len(data)} records")
        else:
            print("✗ Merge failed")
    
    elif args.source:
        print(f"Collecting from {args.source} only...")
        success, message = orchestrator.run_specific_source(args.source)
        print(f"Result: {message}")
    
    else:
        print("Running complete extended collection...")
        success, report = orchestrator.run_extended_collection()
        
        if success:
            print("\n" + "=" * 60)
            print("EXTENDED COLLECTION COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print("\nCheck the data/comprehensive/ directory for results.")
        else:
            print("\n" + "=" * 60)
            print("EXTENDED COLLECTION FAILED")
            print("=" * 60)
            print(f"Error: {report}")


if __name__ == "__main__":
    main()
