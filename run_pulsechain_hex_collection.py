"""
Comprehensive PulseChain and HEX Data Collection Runner
Attempts to collect data using all available free methods
"""
import os
import sys
import logging
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pulsechain_hex_collector import PulseChainHEXCollector
from github_data_sources_finder import GitHubDataSourcesFinder
from alternative_data_sources import AlternativeDataCollector
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/comprehensive_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_banner():
    """Print collection banner"""
    print("🚀 COMPREHENSIVE PULSECHAIN & HEX DATA COLLECTION")
    print("=" * 60)
    print("This script will attempt to collect historical data for:")
    print("• PulseChain (PLS)")
    print("• HEX (PulseChain)")
    print()
    print("Using multiple FREE data sources:")
    print("• CoinGecko API (free tier)")
    print("• CryptoCMD (CoinMarketCap scraper)")
    print("• Yahoo Finance")
    print("• CryptoCompare API")
    print("• GitHub repository search")
    print()
    print("=" * 60)
    print()

def collect_pulsechain_hex_data():
    """Collect PulseChain and HEX specific data"""
    logger.info("Starting PulseChain and HEX specific data collection...")
    
    collector = PulseChainHEXCollector()
    data, summary = collector.collect_all_pulsechain_hex_data()
    
    return data, summary

def collect_general_crypto_data():
    """Collect general crypto data (includes HEX and PLS if available)"""
    logger.info("Starting general cryptocurrency data collection...")
    
    collector = AlternativeDataCollector()
    data, summary = collector.collect_all_sources()
    
    return data, summary

def search_github_sources():
    """Search GitHub for additional data sources"""
    logger.info("Searching GitHub for additional data sources...")
    
    finder = GitHubDataSourcesFinder()
    repositories = finder.find_pulsechain_hex_sources()
    
    if repositories:
        report = finder.generate_report(repositories)
        return repositories, report
    else:
        return [], "No GitHub sources found"

def generate_comprehensive_report(pulse_data, pulse_summary, general_data, general_summary, github_repos, github_report):
    """Generate comprehensive collection report"""
    
    report_lines = [
        "COMPREHENSIVE PULSECHAIN & HEX DATA COLLECTION REPORT",
        "=" * 70,
        f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "COLLECTION RESULTS:",
        "-" * 20
    ]
    
    # PulseChain/HEX specific data
    if not pulse_data.empty:
        pulse_tokens = pulse_data['Symbol'].value_counts() if 'Symbol' in pulse_data.columns else {}
        report_lines.extend([
            f"✅ PulseChain/HEX Specific Data: {len(pulse_data):,} records",
            *[f"   • {token}: {count:,} records" for token, count in pulse_tokens.items()],
            ""
        ])
    else:
        report_lines.extend([
            "❌ PulseChain/HEX Specific Data: No data collected",
            ""
        ])
    
    # General crypto data
    if not general_data.empty:
        # Check for PLS and HEX in general data
        if 'Symbol' in general_data.columns:
            pls_data = general_data[general_data['Symbol'].str.upper() == 'PLS']
            hex_data = general_data[general_data['Symbol'].str.upper() == 'HEX']
            
            report_lines.extend([
                f"✅ General Crypto Data: {len(general_data):,} total records",
                f"   • PLS found in general data: {len(pls_data):,} records",
                f"   • HEX found in general data: {len(hex_data):,} records",
                ""
            ])
        else:
            report_lines.extend([
                f"✅ General Crypto Data: {len(general_data):,} records (no symbol breakdown)",
                ""
            ])
    else:
        report_lines.extend([
            "❌ General Crypto Data: No data collected",
            ""
        ])
    
    # GitHub sources
    if github_repos:
        high_potential = [r for r in github_repos if r['potential_score'] >= 5]
        report_lines.extend([
            f"✅ GitHub Sources Found: {len(github_repos)} repositories analyzed",
            f"   • High potential sources: {len(high_potential)}",
            f"   • Report saved to: github_data_sources_report.txt",
            ""
        ])
    else:
        report_lines.extend([
            "❌ GitHub Sources: No repositories found",
            ""
        ])
    
    # Recommendations
    report_lines.extend([
        "RECOMMENDATIONS:",
        "-" * 16,
        ""
    ])
    
    if not pulse_data.empty or not general_data.empty:
        report_lines.extend([
            "✅ SUCCESS! Data was collected:",
            "• Review the collected data files in the 'data/processed' directory",
            "• Check data quality and coverage for your analysis needs",
            "• Consider setting up automated collection for ongoing updates",
            ""
        ])
    else:
        report_lines.extend([
            "⚠️  LIMITED SUCCESS - No direct data collected:",
            "• PulseChain and HEX may be too new or not widely tracked",
            "• Consider these alternatives:",
            "  - Use PulseChain block explorer APIs directly",
            "  - Set up custom scrapers for PulseChain DEX data",
            "  - Monitor social media and community sources",
            "  - Contact PulseChain community for data sharing",
            ""
        ])
    
    if github_repos:
        top_repos = github_repos[:5]
        report_lines.extend([
            "🔍 EXPLORE THESE GITHUB REPOSITORIES:",
            *[f"• {repo['name']}: {repo['url']}" for repo in top_repos],
            ""
        ])
    
    report_lines.extend([
        "NEXT STEPS:",
        "-" * 12,
        "1. Examine collected data files for completeness",
        "2. Set up automated collection scripts",
        "3. Explore GitHub repositories for additional tools",
        "4. Consider contributing to open source crypto data projects",
        "5. Join PulseChain community for data sharing opportunities",
        "",
        "FILES CREATED:",
        "-" * 14,
        "• data/processed/comprehensive_pulsechain_hex_data.csv",
        "• data/processed/comprehensive_crypto_data.csv", 
        "• github_data_sources_report.txt",
        "• logs/comprehensive_collection.log"
    ])
    
    report_text = "\n".join(report_lines)
    
    # Save comprehensive report
    with open("comprehensive_collection_report.txt", 'w') as f:
        f.write(report_text)
    
    return report_text

def main():
    """Main collection function"""
    print_banner()
    
    # Create directories
    os.makedirs(PROCESSED_DIR, exist_ok=True)
    os.makedirs(LOGS_DIR, exist_ok=True)
    
    # Step 1: Collect PulseChain and HEX specific data
    print("🎯 Step 1: Collecting PulseChain and HEX specific data...")
    pulse_data, pulse_summary = collect_pulsechain_hex_data()
    print("✅ PulseChain/HEX collection complete\n")
    
    # Step 2: Collect general crypto data
    print("📊 Step 2: Collecting general cryptocurrency data...")
    general_data, general_summary = collect_general_crypto_data()
    print("✅ General crypto collection complete\n")
    
    # Step 3: Search GitHub for additional sources
    print("🔍 Step 3: Searching GitHub for additional data sources...")
    github_repos, github_report = search_github_sources()
    print("✅ GitHub search complete\n")
    
    # Step 4: Generate comprehensive report
    print("📋 Step 4: Generating comprehensive report...")
    comprehensive_report = generate_comprehensive_report(
        pulse_data, pulse_summary,
        general_data, general_summary,
        github_repos, github_report
    )
    
    # Final output
    print("\n" + "=" * 70)
    print("🎉 COLLECTION COMPLETE!")
    print("=" * 70)
    print(comprehensive_report)
    
    print(f"\n📁 All files saved to current directory and {PROCESSED_DIR}/")
    print("📋 Check 'comprehensive_collection_report.txt' for full details")

if __name__ == "__main__":
    main()
