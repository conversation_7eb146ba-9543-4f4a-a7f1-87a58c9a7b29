"""
Free PulseChain and HEX Data Scraper
Uses web scraping and free APIs that don't require authentication
"""
import os
import sys
import time
import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import json

from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/free_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FreePulseChainHEXScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def scrape_coinmarketcap_direct(self, symbol):
        """Scrape CoinMarketCap directly for token data"""
        logger.info(f"Scraping CoinMarketCap for {symbol}...")
        
        try:
            # Try different URL patterns
            urls_to_try = [
                f"https://coinmarketcap.com/currencies/{symbol.lower()}/",
                f"https://coinmarketcap.com/currencies/{symbol.lower()}-pulsechain/",
                f"https://coinmarketcap.com/currencies/hex-pulsechain/" if symbol.upper() == 'HEX' else None,
                f"https://coinmarketcap.com/currencies/pulsechain/" if symbol.upper() == 'PLS' else None
            ]
            
            for url in urls_to_try:
                if url is None:
                    continue
                    
                logger.info(f"Trying URL: {url}")
                
                try:
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Look for price data
                        price_element = soup.find('span', class_='sc-f70bb44c-0')
                        if not price_element:
                            price_element = soup.find('div', {'data-role': 'coin-price'})
                        
                        if price_element:
                            price_text = price_element.get_text().strip()
                            logger.info(f"Found current price for {symbol}: {price_text}")
                            
                            # Try to extract numerical price
                            import re
                            price_match = re.search(r'\$?([\d,]+\.?\d*)', price_text)
                            if price_match:
                                price = float(price_match.group(1).replace(',', ''))
                                
                                return {
                                    'Symbol': symbol,
                                    'Current_Price': price,
                                    'Source': 'CoinMarketCap_Scrape',
                                    'Date': datetime.now().strftime('%Y-%m-%d'),
                                    'URL': url
                                }
                    
                    time.sleep(2)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Failed to scrape {url}: {str(e)}")
                    continue
            
            logger.warning(f"No data found for {symbol} on CoinMarketCap")
            return None
            
        except Exception as e:
            logger.error(f"Error scraping CoinMarketCap for {symbol}: {str(e)}")
            return None
    
    def try_alternative_apis(self, symbol):
        """Try alternative free APIs"""
        logger.info(f"Trying alternative APIs for {symbol}...")
        
        # List of free APIs to try (no auth required)
        apis_to_try = [
            {
                'name': 'CryptoCompare',
                'url': f'https://min-api.cryptocompare.com/data/price?fsym={symbol}&tsyms=USD',
                'parser': lambda data: {'price': data.get('USD')} if 'USD' in data else None
            },
            {
                'name': 'Coinlore',
                'url': f'https://api.coinlore.net/api/ticker/?id={symbol}',
                'parser': lambda data: {'price': float(data[0]['price_usd'])} if data and len(data) > 0 else None
            }
        ]
        
        for api in apis_to_try:
            try:
                logger.info(f"Trying {api['name']} API...")
                response = self.session.get(api['url'], timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    parsed = api['parser'](data)
                    
                    if parsed and 'price' in parsed:
                        logger.info(f"✅ {api['name']}: Found price {parsed['price']} for {symbol}")
                        return {
                            'Symbol': symbol,
                            'Current_Price': parsed['price'],
                            'Source': api['name'],
                            'Date': datetime.now().strftime('%Y-%m-%d')
                        }
                
                time.sleep(1)
                
            except Exception as e:
                logger.warning(f"{api['name']} failed for {symbol}: {str(e)}")
                continue
        
        return None
    
    def scrape_dexscreener(self, symbol):
        """Try to get data from DexScreener (DEX aggregator)"""
        logger.info(f"Trying DexScreener for {symbol}...")
        
        try:
            # DexScreener search
            search_url = f"https://api.dexscreener.com/latest/dex/search/?q={symbol}"
            
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                pairs = data.get('pairs', [])
                
                # Look for PulseChain pairs
                pulsechain_pairs = [p for p in pairs if 'pulse' in p.get('chainId', '').lower()]
                
                if pulsechain_pairs:
                    pair = pulsechain_pairs[0]  # Take first match
                    price = float(pair.get('priceUsd', 0))
                    
                    if price > 0:
                        logger.info(f"✅ DexScreener: Found price {price} for {symbol}")
                        return {
                            'Symbol': symbol,
                            'Current_Price': price,
                            'Source': 'DexScreener',
                            'Date': datetime.now().strftime('%Y-%m-%d'),
                            'Chain': pair.get('chainId'),
                            'DEX': pair.get('dexId')
                        }
            
        except Exception as e:
            logger.warning(f"DexScreener failed for {symbol}: {str(e)}")
        
        return None
    
    def collect_current_data(self):
        """Collect current price data for PulseChain and HEX"""
        logger.info("Collecting current PulseChain and HEX data...")
        
        tokens = ['PLS', 'HEX']
        all_data = []
        
        for token in tokens:
            logger.info(f"Collecting data for {token}...")
            
            # Try multiple methods
            methods = [
                self.scrape_dexscreener,
                self.try_alternative_apis,
                self.scrape_coinmarketcap_direct
            ]
            
            for method in methods:
                try:
                    result = method(token)
                    if result:
                        all_data.append(result)
                        logger.info(f"✅ Successfully collected {token} data via {result['Source']}")
                        break
                except Exception as e:
                    logger.warning(f"Method {method.__name__} failed for {token}: {str(e)}")
                    continue
            else:
                logger.warning(f"❌ No data collected for {token}")
        
        if all_data:
            df = pd.DataFrame(all_data)
            output_file = f"{PROCESSED_DIR}/free_pulsechain_hex_current_data.csv"
            df.to_csv(output_file, index=False)
            logger.info(f"Saved current data: {len(df)} records to {output_file}")
            return df
        else:
            logger.warning("No current data collected")
            return pd.DataFrame()
    
    def generate_manual_data_guide(self):
        """Generate a guide for manual data collection"""
        guide_lines = [
            "Manual PulseChain and HEX Data Collection Guide",
            "=" * 50,
            "",
            "Since automated collection is limited, here are manual options:",
            "",
            "1. PULSECHAIN BLOCK EXPLORER:",
            "   • Visit: https://scan.pulsechain.com/",
            "   • Search for PLS token contract",
            "   • Export transaction history",
            "",
            "2. DEX DATA:",
            "   • PulseX (main PulseChain DEX): https://app.pulsex.com/",
            "   • DexScreener: https://dexscreener.com/pulsechain",
            "   • Look for PLS/USDC or HEX/USDC pairs",
            "",
            "3. COMMUNITY SOURCES:",
            "   • PulseChain community Discord/Telegram",
            "   • Reddit: r/PulseChain, r/HEXcrypto",
            "   • Twitter: @PulseChainCom, @RichardHeartWin",
            "",
            "4. THIRD-PARTY TOOLS:",
            "   • CoinTracker (may have PLS/HEX)",
            "   • Portfolio tracking apps",
            "   • Custom blockchain indexers",
            "",
            "5. DIRECT BLOCKCHAIN QUERIES:",
            "   • Use PulseChain RPC endpoints",
            "   • Query token contracts directly",
            "   • Build custom indexer with web3.py",
            "",
            "6. DATA SHARING:",
            "   • Join PulseChain developer communities",
            "   • Collaborate with other researchers",
            "   • Share and request datasets",
            "",
            "RECOMMENDED NEXT STEPS:",
            "• Set up PulseChain node for direct data access",
            "• Create custom scraper for PulseX DEX",
            "• Monitor social media for data sharing opportunities",
            "• Consider premium APIs if budget allows"
        ]
        
        guide_text = "\n".join(guide_lines)
        
        with open("manual_data_collection_guide.txt", 'w') as f:
            f.write(guide_text)
        
        return guide_text
    
    def run_comprehensive_collection(self):
        """Run comprehensive free data collection"""
        logger.info("Starting comprehensive free PulseChain and HEX collection...")
        
        # Collect current data
        current_data = self.collect_current_data()
        
        # Generate manual guide
        manual_guide = self.generate_manual_data_guide()
        
        # Generate summary
        summary_lines = [
            "Free PulseChain and HEX Data Collection Summary",
            "=" * 50,
            f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "RESULTS:",
            f"• Current data points collected: {len(current_data)}",
            ""
        ]
        
        if not current_data.empty:
            summary_lines.extend([
                "✅ SUCCESS - Current prices found:",
                *[f"   • {row['Symbol']}: ${row['Current_Price']} ({row['Source']})" 
                  for _, row in current_data.iterrows()],
                ""
            ])
        else:
            summary_lines.extend([
                "⚠️  LIMITED SUCCESS - No current prices found",
                "• PulseChain and HEX may not be widely tracked yet",
                "• Consider manual collection methods (see guide)",
                ""
            ])
        
        summary_lines.extend([
            "FILES CREATED:",
            "• free_pulsechain_hex_current_data.csv (if data found)",
            "• manual_data_collection_guide.txt",
            "• logs/free_scraper.log",
            "",
            "NEXT STEPS:",
            "• Review manual collection guide",
            "• Join PulseChain community for data sharing",
            "• Consider setting up custom blockchain indexer"
        ])
        
        summary_text = "\n".join(summary_lines)
        
        with open("free_collection_summary.txt", 'w') as f:
            f.write(summary_text)
        
        return current_data, summary_text

if __name__ == "__main__":
    scraper = FreePulseChainHEXScraper()
    
    print("🆓 Free PulseChain and HEX Data Scraper")
    print("=" * 45)
    print("Attempting to collect data without API keys...")
    print("This may have limited success due to API restrictions.")
    print()
    
    data, summary = scraper.run_comprehensive_collection()
    
    print("\n" + "=" * 45)
    print("🎯 COLLECTION COMPLETE!")
    print("=" * 45)
    print(summary)
    
    if not data.empty:
        print(f"\n📊 Current data saved to: {PROCESSED_DIR}/free_pulsechain_hex_current_data.csv")
    
    print("📋 Manual collection guide: manual_data_collection_guide.txt")
    print("📋 Full summary: free_collection_summary.txt")
