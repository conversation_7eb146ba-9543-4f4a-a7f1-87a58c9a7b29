"""
Alternative Data Sources for Historical Cryptocurrency Data
Implements multiple approaches to get maximum historical data coverage
"""
import os
import pandas as pd
import requests
import time
from datetime import datetime, timedelta
import logging
from tqdm import tqdm
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/alternative_sources.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AlternativeDataCollector:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def collect_cryptocmd_data(self):
        """Use CryptoCMD library to scrape CoinMarketCap data"""
        logger.info("Starting CryptoCMD data collection...")

        try:
            from cryptocmd import CmcScraper

            # List of major cryptocurrencies to collect data for
            major_cryptos = [
                'BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'SOL', 'DOGE', 'DOT', 'AVAX', 'SHIB',
                'MATIC', 'LTC', 'UNI', 'LINK', 'BCH', 'ALGO', 'VET', 'ICP', 'FIL', 'TRX',
                'ETC', 'XLM', 'THETA', 'HBAR', 'NEAR', 'FLOW', 'EGLD', 'MANA', 'SAND', 'AXS',
                # Adding PulseChain ecosystem tokens
                'HEX', 'PLS'
            ]
            
            all_data = []
            start_date = START_DATE.strftime("%d-%m-%Y")
            end_date = END_DATE.strftime("%d-%m-%Y")
            
            for crypto in tqdm(major_cryptos, desc="Collecting crypto data"):
                try:
                    scraper = CmcScraper(crypto, start_date, end_date)
                    df = scraper.get_dataframe()
                    
                    if not df.empty:
                        df['Symbol'] = crypto
                        df['Source'] = 'CryptoCMD'
                        all_data.append(df)
                        logger.info(f"Collected {len(df)} records for {crypto}")
                    
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Failed to collect data for {crypto}: {str(e)}")
                    continue
            
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                output_file = f"{PROCESSED_DIR}/cryptocmd_historical_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved CryptoCMD data: {len(combined_df)} records to {output_file}")
                return combined_df
            
        except ImportError:
            logger.error("CryptoCMD library not available. Install with: pip install cryptocmd")
        except Exception as e:
            logger.error(f"Error in CryptoCMD collection: {str(e)}")
        
        return pd.DataFrame()

    def collect_pulsechain_hex_data(self):
        """Collect PulseChain and HEX historical data using CoinGecko API"""
        logger.info("Starting PulseChain and HEX data collection from CoinGecko...")

        try:
            import requests
            from datetime import datetime, timedelta

            # CoinGecko API endpoints
            base_url = "https://api.coingecko.com/api/v3"

            # Token configurations
            tokens = {
                'pulsechain': {
                    'id': 'pulsechain',
                    'symbol': 'PLS',
                    'name': 'PulseChain'
                },
                'hex-pulsechain': {
                    'id': 'hex-pulsechain',
                    'symbol': 'HEX',
                    'name': 'HEX (PulseChain)'
                }
            }

            all_data = []

            for token_key, token_info in tokens.items():
                logger.info(f"Collecting data for {token_info['name']} ({token_info['symbol']})...")

                try:
                    # Get historical market data (price, market cap, volume)
                    # CoinGecko free API allows up to 365 days of historical data
                    url = f"{base_url}/coins/{token_info['id']}/market_chart"
                    params = {
                        'vs_currency': 'usd',
                        'days': '365',  # Get last 365 days
                        'interval': 'daily'
                    }

                    response = requests.get(url, params=params)

                    if response.status_code == 200:
                        data = response.json()

                        # Extract price, market cap, and volume data
                        prices = data.get('prices', [])
                        market_caps = data.get('market_caps', [])
                        volumes = data.get('total_volumes', [])

                        # Convert to DataFrame format
                        for i, (timestamp, price) in enumerate(prices):
                            date = datetime.fromtimestamp(timestamp / 1000)

                            market_cap = market_caps[i][1] if i < len(market_caps) else None
                            volume = volumes[i][1] if i < len(volumes) else None

                            record = {
                                'Date': date.strftime('%Y-%m-%d'),
                                'Symbol': token_info['symbol'],
                                'Name': token_info['name'],
                                'Price': price,
                                'Market_Cap': market_cap,
                                'Volume': volume,
                                'Source': 'CoinGecko',
                                'Timestamp': timestamp
                            }
                            all_data.append(record)

                        logger.info(f"Collected {len(prices)} records for {token_info['symbol']}")

                    else:
                        logger.warning(f"Failed to fetch data for {token_info['symbol']}: HTTP {response.status_code}")

                    # Rate limiting - CoinGecko allows 10-50 calls per minute for free tier
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"Error collecting data for {token_info['symbol']}: {str(e)}")
                    continue

            if all_data:
                df = pd.DataFrame(all_data)
                output_file = f"{PROCESSED_DIR}/pulsechain_hex_historical_data.csv"
                df.to_csv(output_file, index=False)
                logger.info(f"Saved PulseChain/HEX data: {len(df)} records to {output_file}")
                return df
            else:
                logger.warning("No PulseChain/HEX data collected")
                return pd.DataFrame()

        except ImportError:
            logger.error("Required libraries not available. Install with: pip install requests")
        except Exception as e:
            logger.error(f"Error in PulseChain/HEX collection: {str(e)}")

        return pd.DataFrame()

    def collect_extended_pulsechain_hex_data(self):
        """Try multiple sources for PulseChain and HEX data with extended historical coverage"""
        logger.info("Starting extended PulseChain and HEX data collection...")

        all_datasets = []

        # Method 1: Try CryptoCMD for HEX (if available on CMC)
        try:
            from cryptocmd import CmcScraper

            hex_tokens = ['HEX']  # Try different variations

            for token in hex_tokens:
                try:
                    logger.info(f"Trying CryptoCMD for {token}...")
                    start_date = START_DATE.strftime("%d-%m-%Y")
                    end_date = END_DATE.strftime("%d-%m-%Y")

                    scraper = CmcScraper(token, start_date, end_date)
                    df = scraper.get_dataframe()

                    if not df.empty:
                        df['Symbol'] = token
                        df['Source'] = 'CryptoCMD'
                        all_datasets.append(df)
                        logger.info(f"CryptoCMD: Collected {len(df)} records for {token}")

                    time.sleep(2)

                except Exception as e:
                    logger.warning(f"CryptoCMD failed for {token}: {str(e)}")
                    continue

        except ImportError:
            logger.info("CryptoCMD not available, skipping...")

        # Method 2: Try CoinGecko with different date ranges
        try:
            import requests

            tokens = {
                'pulsechain': 'PLS',
                'hex-pulsechain': 'HEX'
            }

            for token_id, symbol in tokens.items():
                try:
                    logger.info(f"Trying extended CoinGecko data for {symbol}...")

                    # Try to get maximum available historical data
                    url = f"https://api.coingecko.com/api/v3/coins/{token_id}/market_chart"
                    params = {
                        'vs_currency': 'usd',
                        'days': 'max',  # Get all available historical data
                        'interval': 'daily'
                    }

                    response = requests.get(url, params=params)

                    if response.status_code == 200:
                        data = response.json()
                        prices = data.get('prices', [])

                        if prices:
                            records = []
                            for timestamp, price in prices:
                                date = datetime.fromtimestamp(timestamp / 1000)
                                records.append({
                                    'Date': date,
                                    'Close': price,
                                    'Symbol': symbol,
                                    'Source': 'CoinGecko_Extended'
                                })

                            df = pd.DataFrame(records)
                            all_datasets.append(df)
                            logger.info(f"CoinGecko Extended: Collected {len(df)} records for {symbol}")

                    time.sleep(3)  # Longer delay for extended requests

                except Exception as e:
                    logger.warning(f"Extended CoinGecko failed for {symbol}: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"Extended CoinGecko collection error: {str(e)}")

        # Combine all datasets
        if all_datasets:
            combined_df = pd.concat(all_datasets, ignore_index=True)
            output_file = f"{PROCESSED_DIR}/extended_pulsechain_hex_data.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"Saved extended PulseChain/HEX data: {len(combined_df)} records to {output_file}")
            return combined_df
        else:
            logger.warning("No extended PulseChain/HEX data collected")
            return pd.DataFrame()

    def collect_yahoo_finance_data(self):
        """Use Yahoo Finance to get cryptocurrency data"""
        logger.info("Starting Yahoo Finance data collection...")
        
        try:
            import yfinance as yf
            
            # Yahoo Finance crypto symbols
            yahoo_cryptos = [
                'BTC-USD', 'ETH-USD', 'BNB-USD', 'XRP-USD', 'ADA-USD', 'SOL-USD',
                'DOGE-USD', 'DOT-USD', 'AVAX-USD', 'SHIB-USD', 'MATIC-USD', 'LTC-USD',
                'UNI-USD', 'LINK-USD', 'BCH-USD', 'ALGO-USD', 'VET-USD', 'ICP-USD'
            ]
            
            all_data = []
            start_date = START_DATE.strftime("%Y-%m-%d")
            end_date = END_DATE.strftime("%Y-%m-%d")
            
            for symbol in tqdm(yahoo_cryptos, desc="Collecting Yahoo Finance data"):
                try:
                    ticker = yf.Ticker(symbol)
                    df = ticker.history(start=start_date, end=end_date)
                    
                    if not df.empty:
                        df = df.reset_index()
                        df['Symbol'] = symbol.replace('-USD', '')
                        df['Source'] = 'Yahoo Finance'
                        df['Market_Cap'] = df['Close'] * df['Volume']  # Approximation
                        all_data.append(df)
                        logger.info(f"Collected {len(df)} records for {symbol}")
                    
                    time.sleep(0.5)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Failed to collect Yahoo data for {symbol}: {str(e)}")
                    continue
            
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                output_file = f"{PROCESSED_DIR}/yahoo_finance_crypto_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved Yahoo Finance data: {len(combined_df)} records to {output_file}")
                return combined_df
                
        except ImportError:
            logger.error("yfinance library not available. Install with: pip install yfinance")
        except Exception as e:
            logger.error(f"Error in Yahoo Finance collection: {str(e)}")
        
        return pd.DataFrame()
    
    def collect_cryptocompare_data(self):
        """Use CryptoCompare API for historical data"""
        logger.info("Starting CryptoCompare data collection...")
        
        try:
            base_url = "https://min-api.cryptocompare.com/data/v2/histoday"
            
            major_cryptos = ['BTC', 'ETH', 'BNB', 'XRP', 'ADA', 'SOL', 'DOGE', 'DOT', 'AVAX', 'SHIB']
            all_data = []
            
            # Calculate days between start and end date
            days_diff = (END_DATE - START_DATE).days
            
            for crypto in tqdm(major_cryptos, desc="Collecting CryptoCompare data"):
                try:
                    params = {
                        'fsym': crypto,
                        'tsym': 'USD',
                        'limit': min(days_diff, 2000),  # API limit
                        'toTs': int(END_DATE.timestamp())
                    }
                    
                    response = self.session.get(base_url, params=params)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if data.get('Response') == 'Success':
                            df = pd.DataFrame(data['Data']['Data'])
                            
                            if not df.empty:
                                # Convert timestamp to date
                                df['Date'] = pd.to_datetime(df['time'], unit='s')
                                df['Symbol'] = crypto
                                df['Source'] = 'CryptoCompare'
                                
                                # Rename columns to match our format
                                df = df.rename(columns={
                                    'high': 'High',
                                    'low': 'Low',
                                    'open': 'Open',
                                    'close': 'Close',
                                    'volumefrom': 'Volume'
                                })
                                
                                all_data.append(df)
                                logger.info(f"Collected {len(df)} records for {crypto}")
                    
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Failed to collect CryptoCompare data for {crypto}: {str(e)}")
                    continue
            
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                output_file = f"{PROCESSED_DIR}/cryptocompare_historical_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved CryptoCompare data: {len(combined_df)} records to {output_file}")
                return combined_df
                
        except Exception as e:
            logger.error(f"Error in CryptoCompare collection: {str(e)}")
        
        return pd.DataFrame()
    
    def download_kaggle_datasets(self):
        """Download and process Kaggle cryptocurrency datasets"""
        logger.info("Starting Kaggle dataset collection...")
        
        try:
            import kaggle
            
            # List of relevant Kaggle datasets
            datasets = [
                'bizzyvinci/coinmarketcap-historical-data',
                'sudalairajkumar/cryptocurrencypricehistory',
                'maharshipandya/-cryptocurrency-historical-prices-dataset'
            ]
            
            kaggle_data = []
            
            for dataset in datasets:
                try:
                    logger.info(f"Downloading Kaggle dataset: {dataset}")
                    
                    # Download dataset
                    kaggle.api.dataset_download_files(
                        dataset, 
                        path=f"{DATA_DIR}/kaggle_temp", 
                        unzip=True
                    )
                    
                    # Process downloaded files
                    temp_dir = f"{DATA_DIR}/kaggle_temp"
                    if os.path.exists(temp_dir):
                        for file in os.listdir(temp_dir):
                            if file.endswith('.csv'):
                                filepath = os.path.join(temp_dir, file)
                                df = pd.read_csv(filepath)
                                df['Source'] = f'Kaggle_{dataset.split("/")[1]}'
                                kaggle_data.append(df)
                                logger.info(f"Processed {file}: {len(df)} records")
                    
                except Exception as e:
                    logger.warning(f"Failed to download {dataset}: {str(e)}")
                    continue
            
            if kaggle_data:
                combined_df = pd.concat(kaggle_data, ignore_index=True)
                output_file = f"{PROCESSED_DIR}/kaggle_combined_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved Kaggle data: {len(combined_df)} records to {output_file}")
                return combined_df
                
        except ImportError:
            logger.error("Kaggle library not available. Install with: pip install kaggle")
            logger.info("Also set up Kaggle API credentials: https://github.com/Kaggle/kaggle-api")
        except Exception as e:
            logger.error(f"Error in Kaggle collection: {str(e)}")
        
        return pd.DataFrame()
    
    def collect_all_sources(self):
        """Collect data from all available sources"""
        logger.info("Starting comprehensive data collection from all sources...")
        
        all_datasets = []
        
        # Collect from each source
        sources = [
            ("CryptoCMD", self.collect_cryptocmd_data),
            ("Yahoo Finance", self.collect_yahoo_finance_data),
            ("CryptoCompare", self.collect_cryptocompare_data),
            ("PulseChain/HEX (CoinGecko)", self.collect_pulsechain_hex_data),
            ("Extended PulseChain/HEX", self.collect_extended_pulsechain_hex_data),
            ("Kaggle", self.download_kaggle_datasets)
        ]
        
        for source_name, collect_func in sources:
            logger.info(f"Collecting from {source_name}...")
            try:
                df = collect_func()
                if not df.empty:
                    all_datasets.append(df)
                    logger.info(f"✓ {source_name}: {len(df)} records collected")
                else:
                    logger.warning(f"✗ {source_name}: No data collected")
            except Exception as e:
                logger.error(f"✗ {source_name}: Collection failed - {str(e)}")
        
        # Combine all data
        if all_datasets:
            logger.info("Combining data from all sources...")
            combined_df = pd.concat(all_datasets, ignore_index=True, sort=False)
            
            # Save combined dataset
            output_file = f"{PROCESSED_DIR}/comprehensive_crypto_data.csv"
            combined_df.to_csv(output_file, index=False)
            
            # Generate summary
            summary = self.generate_collection_summary(combined_df)
            
            logger.info(f"✓ Comprehensive collection complete: {len(combined_df)} total records")
            return combined_df, summary
        else:
            logger.error("No data collected from any source")
            return pd.DataFrame(), "No data collected"
    
    def generate_collection_summary(self, df):
        """Generate summary of collected data"""
        if df.empty:
            return "No data available"
        
        summary_lines = [
            "Comprehensive Cryptocurrency Data Collection Summary",
            "=" * 60,
            f"Total Records: {len(df):,}",
            f"Date Range: {df.get('Date', df.get('time', pd.Series())).min()} to {df.get('Date', df.get('time', pd.Series())).max()}",
            ""
        ]
        
        # Source breakdown
        if 'Source' in df.columns:
            source_counts = df['Source'].value_counts()
            summary_lines.extend([
                "Records by Source:",
                *[f"  {source}: {count:,}" for source, count in source_counts.items()],
                ""
            ])
        
        # Symbol breakdown
        if 'Symbol' in df.columns:
            symbol_counts = df['Symbol'].value_counts().head(20)
            summary_lines.extend([
                "Top 20 Cryptocurrencies by Record Count:",
                *[f"  {symbol}: {count:,}" for symbol, count in symbol_counts.items()],
                ""
            ])
        
        summary_text = "\n".join(summary_lines)
        
        # Save summary
        with open(f"{PROCESSED_DIR}/collection_summary.txt", 'w') as f:
            f.write(summary_text)
        
        return summary_text

if __name__ == "__main__":
    collector = AlternativeDataCollector()
    
    print("Starting comprehensive cryptocurrency data collection...")
    print("This will attempt to collect data from multiple sources:")
    print("- CryptoCMD (CoinMarketCap scraper) - includes HEX and PLS")
    print("- Yahoo Finance")
    print("- CryptoCompare API")
    print("- PulseChain/HEX data from CoinGecko API")
    print("- Extended PulseChain/HEX historical data")
    print("- Kaggle datasets")
    print()
    
    combined_data, summary = collector.collect_all_sources()
    
    print("\n" + "="*60)
    print("COLLECTION COMPLETE!")
    print("="*60)
    print(summary)
