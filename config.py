"""
Configuration settings for crypto data collection
"""
from datetime import datetime, timedelta

# Data collection settings
START_DATE = datetime(2020, 10, 1)  # October 2020 - full 5 year range
END_DATE = datetime(2024, 10, 3)    # October 3rd, 2024 (or closest date)

# CoinMarketCap settings
CMC_BASE_URL = "https://coinmarketcap.com/historical/"
CMC_EXPORT_URL = "https://web-api.coinmarketcap.com/v1/cryptocurrency/listings/historical"

# Rate limiting settings
REQUEST_DELAY = 2  # seconds between requests to avoid being blocked
MAX_RETRIES = 3

# File paths
DATA_DIR = "data"
SNAPSHOTS_DIR = f"{DATA_DIR}/snapshots"
PROCESSED_DIR = f"{DATA_DIR}/processed"
LOGS_DIR = "logs"

# Output settings
NEW_LISTINGS_OUTPUT = f"{PROCESSED_DIR}/new_crypto_listings_2020-2024.csv"
FULL_DATA_OUTPUT = f"{PROCESSED_DIR}/full_historical_data.csv"

# CoinGecko API settings
COINGECKO_API_DELAY = 1.2  # seconds between API calls (free tier limit)
COINGECKO_MAX_RETRIES = 3
