"""
Install dependencies for PulseChain and HEX data collection
"""
import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def main():
    print("🔧 Installing dependencies for PulseChain and HEX data collection...")
    print("=" * 60)
    
    # Required packages
    packages = [
        "requests",
        "pandas", 
        "tqdm",
        "cryptocmd",
        "yfinance",
        "beautifulsoup4",
        "lxml"
    ]
    
    # Optional packages
    optional_packages = [
        "kaggle",
        "python-dotenv"
    ]
    
    print("Installing required packages...")
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 Required packages: {success_count}/{len(packages)} installed successfully")
    
    print("\nInstalling optional packages...")
    optional_success = 0
    
    for package in optional_packages:
        if install_package(package):
            optional_success += 1
    
    print(f"📊 Optional packages: {optional_success}/{len(optional_packages)} installed successfully")
    
    if success_count == len(packages):
        print("\n✅ All required dependencies installed successfully!")
        print("You can now run: python run_pulsechain_hex_collection.py")
    else:
        print(f"\n⚠️  Some required packages failed to install.")
        print("Please install them manually using:")
        print("pip install requests pandas tqdm cryptocmd yfinance beautifulsoup4 lxml")
    
    print("\n📋 Optional setup:")
    print("• For Kaggle datasets: Set up Kaggle API credentials")
    print("• For enhanced rate limiting: Consider getting API keys for data sources")

if __name__ == "__main__":
    main()
