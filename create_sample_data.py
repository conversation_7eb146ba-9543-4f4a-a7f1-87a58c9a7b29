"""
Create sample data for demonstration purposes
"""
import os
import pandas as pd
from datetime import datetime, timedelta
import random
from config import SNAPSHOTS_DIR

# Create sample cryptocurrency data
def create_sample_snapshot(date, num_coins=100):
    """Create a sample snapshot with realistic crypto data"""
    
    # Base coins that should appear in most snapshots
    base_coins = [
        ("Bitcoin", "BTC", 50000, 1000000000000),
        ("Ethereum", "ETH", 3000, 400000000000),
        ("Binance Coin", "BNB", 300, 50000000000),
        ("Cardano", "ADA", 0.5, 20000000000),
        ("Solana", "SOL", 100, 30000000000),
        ("XRP", "XRP", 0.6, 30000000000),
        ("Polkadot", "DOT", 20, 20000000000),
        ("Dogecoin", "DOGE", 0.08, 10000000000),
        ("Avalanche", "AVAX", 30, 10000000000),
        ("Chainlink", "LINK", 15, 8000000000),
    ]
    
    # Generate additional coins with some being "new" based on date
    additional_coins = []
    
    # Simulate new coins appearing over time
    date_factor = (date - datetime(2024, 9, 1)).days
    
    # Add some coins that appear later (simulating new listings)
    if date_factor >= 7:  # After first week
        additional_coins.extend([
            ("NewCoin Alpha", "NEWA", 1.5, 500000000),
            ("Beta Token", "BETA", 0.25, 250000000),
        ])
    
    if date_factor >= 14:  # After two weeks
        additional_coins.extend([
            ("Gamma Protocol", "GAMMA", 5.0, 1000000000),
            ("Delta Finance", "DELTA", 0.75, 750000000),
        ])
    
    if date_factor >= 21:  # After three weeks
        additional_coins.extend([
            ("Epsilon Coin", "EPS", 2.5, 2500000000),
            ("Zeta Network", "ZETA", 1.25, 1250000000),
        ])
    
    # Combine all coins
    all_coins = base_coins + additional_coins
    
    # Add some random variation to prices and market caps
    snapshot_data = []
    for i, (name, symbol, base_price, base_mcap) in enumerate(all_coins[:num_coins]):
        # Add some random variation (±20%)
        price_variation = random.uniform(0.8, 1.2)
        mcap_variation = random.uniform(0.8, 1.2)
        
        price = base_price * price_variation
        market_cap = base_mcap * mcap_variation
        volume_24h = market_cap * random.uniform(0.05, 0.3)  # 5-30% of market cap
        
        snapshot_data.append({
            "Rank": i + 1,
            "Name": name,
            "Symbol": symbol,
            "Price": f"${price:.6f}",
            "Market Cap": f"${market_cap:,.0f}",
            "Volume (24h)": f"${volume_24h:,.0f}",
            "Circulating Supply": f"{market_cap/price:,.0f}",
            "Change (24h)": f"{random.uniform(-10, 10):.2f}%"
        })
    
    return pd.DataFrame(snapshot_data)

def create_sample_snapshots():
    """Create sample snapshots for testing"""
    os.makedirs(SNAPSHOTS_DIR, exist_ok=True)
    
    # Create snapshots for the configured date range
    start_date = datetime(2024, 9, 1)
    end_date = datetime(2024, 10, 3)
    
    current_date = start_date
    snapshots_created = 0
    
    while current_date <= end_date:
        date_str = current_date.strftime("%Y%m%d")
        filename = f"{SNAPSHOTS_DIR}/{date_str}.csv"
        
        # Create snapshot data
        df = create_sample_snapshot(current_date)
        
        # Save to CSV
        df.to_csv(filename, index=False)
        print(f"Created sample snapshot: {filename} ({len(df)} coins)")
        
        snapshots_created += 1
        current_date += timedelta(weeks=1)  # Weekly snapshots
    
    print(f"\nCreated {snapshots_created} sample snapshots")
    return snapshots_created

if __name__ == "__main__":
    print("Creating sample cryptocurrency data for demonstration...")
    create_sample_snapshots()
    print("Sample data creation complete!")
