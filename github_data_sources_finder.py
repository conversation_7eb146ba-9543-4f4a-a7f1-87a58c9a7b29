"""
GitHub Data Sources Finder
Searches for GitHub repositories that might contain PulseChain and HEX data
"""
import requests
import json
import time
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GitHubDataSourcesFinder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PulseChain-HEX-Data-Collector/1.0',
            'Accept': 'application/vnd.github.v3+json'
        })
    
    def search_repositories(self, query, max_results=50):
        """Search GitHub repositories for relevant data sources"""
        logger.info(f"Searching GitHub for: {query}")
        
        url = "https://api.github.com/search/repositories"
        params = {
            'q': query,
            'sort': 'stars',
            'order': 'desc',
            'per_page': min(max_results, 100)
        }
        
        try:
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                repositories = data.get('items', [])
                
                logger.info(f"Found {len(repositories)} repositories")
                return repositories
            else:
                logger.error(f"GitHub API error: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error searching GitHub: {str(e)}")
            return []
    
    def analyze_repository(self, repo):
        """Analyze a repository for data collection potential"""
        analysis = {
            'name': repo['full_name'],
            'description': repo.get('description', ''),
            'stars': repo['stargazers_count'],
            'language': repo.get('language', 'Unknown'),
            'updated': repo['updated_at'],
            'url': repo['html_url'],
            'clone_url': repo['clone_url'],
            'topics': repo.get('topics', []),
            'has_data_files': False,
            'potential_score': 0
        }
        
        # Score based on various factors
        score = 0
        
        # Language preferences
        if analysis['language'] in ['Python', 'JavaScript', 'TypeScript']:
            score += 2
        
        # Keywords in description
        description_lower = (analysis['description'] or '').lower()
        keywords = ['data', 'historical', 'price', 'crypto', 'api', 'scraper', 'dataset']
        for keyword in keywords:
            if keyword in description_lower:
                score += 1

        # PulseChain/HEX specific keywords
        pulse_keywords = ['pulsechain', 'hex', 'pulse', 'richard heart']
        for keyword in pulse_keywords:
            if keyword in description_lower:
                score += 3
        
        # Stars (popularity indicator)
        if analysis['stars'] > 100:
            score += 2
        elif analysis['stars'] > 10:
            score += 1
        
        # Recent activity
        updated_date = datetime.fromisoformat(analysis['updated'].replace('Z', '+00:00'))
        days_since_update = (datetime.now(updated_date.tzinfo) - updated_date).days
        if days_since_update < 30:
            score += 2
        elif days_since_update < 365:
            score += 1
        
        analysis['potential_score'] = score
        return analysis
    
    def find_pulsechain_hex_sources(self):
        """Find GitHub repositories related to PulseChain and HEX data"""
        logger.info("🔍 Searching for PulseChain and HEX data sources on GitHub...")
        
        # Search queries
        queries = [
            'PulseChain data',
            'HEX cryptocurrency data',
            'PulseChain historical price',
            'HEX price data',
            'PulseChain API',
            'cryptocurrency data scraper python',
            'crypto historical data free',
            'coinmarketcap scraper',
            'coingecko api python',
            'blockchain data collection'
        ]
        
        all_repositories = []
        
        for query in queries:
            repos = self.search_repositories(query, max_results=20)
            all_repositories.extend(repos)
            time.sleep(1)  # Rate limiting
        
        # Remove duplicates
        unique_repos = {}
        for repo in all_repositories:
            unique_repos[repo['id']] = repo
        
        logger.info(f"Found {len(unique_repos)} unique repositories")
        
        # Analyze repositories
        analyzed_repos = []
        for repo in unique_repos.values():
            analysis = self.analyze_repository(repo)
            analyzed_repos.append(analysis)
        
        # Sort by potential score
        analyzed_repos.sort(key=lambda x: x['potential_score'], reverse=True)
        
        return analyzed_repos
    
    def generate_report(self, repositories):
        """Generate a report of potential data sources"""
        report_lines = [
            "GitHub Data Sources Report for PulseChain and HEX",
            "=" * 60,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total repositories analyzed: {len(repositories)}",
            "",
            "TOP POTENTIAL DATA SOURCES:",
            "-" * 30
        ]
        
        # Show top 20 repositories
        top_repos = repositories[:20]
        
        for i, repo in enumerate(top_repos, 1):
            report_lines.extend([
                f"{i}. {repo['name']} (Score: {repo['potential_score']})",
                f"   Description: {repo['description'][:100]}{'...' if len(repo['description']) > 100 else ''}",
                f"   Language: {repo['language']} | Stars: {repo['stars']} | Updated: {repo['updated'][:10]}",
                f"   URL: {repo['url']}",
                f"   Clone: {repo['clone_url']}",
                ""
            ])
        
        # Categories
        report_lines.extend([
            "",
            "REPOSITORIES BY CATEGORY:",
            "-" * 30
        ])
        
        # High potential (score >= 5)
        high_potential = [r for r in repositories if r['potential_score'] >= 5]
        if high_potential:
            report_lines.extend([
                f"🔥 HIGH POTENTIAL ({len(high_potential)} repos):",
                *[f"   • {r['name']} - {r['description'][:80]}{'...' if len(r['description']) > 80 else ''}" for r in high_potential[:10]],
                ""
            ])
        
        # Python repositories
        python_repos = [r for r in repositories if r['language'] == 'Python']
        if python_repos:
            report_lines.extend([
                f"🐍 PYTHON REPOSITORIES ({len(python_repos)} repos):",
                *[f"   • {r['name']} - Score: {r['potential_score']}" for r in python_repos[:10]],
                ""
            ])
        
        # Recently updated
        recent_repos = [r for r in repositories if 'days_since_update' in r and r.get('days_since_update', 999) < 90]
        if recent_repos:
            report_lines.extend([
                f"🕒 RECENTLY UPDATED ({len(recent_repos)} repos):",
                *[f"   • {r['name']} - Updated: {r['updated'][:10]}" for r in recent_repos[:10]],
                ""
            ])
        
        report_lines.extend([
            "",
            "RECOMMENDED ACTIONS:",
            "-" * 20,
            "1. Review high-potential repositories for existing data or APIs",
            "2. Check Python repositories for reusable code",
            "3. Look for CSV/JSON data files in repository contents",
            "4. Contact repository owners for data sharing opportunities",
            "5. Fork and extend existing crypto data collectors",
            "",
            "NEXT STEPS:",
            "-" * 12,
            "• Clone promising repositories and examine their data collection methods",
            "• Check for existing datasets in repository releases or data folders",
            "• Adapt existing scrapers to include PulseChain and HEX",
            "• Contribute PulseChain/HEX support to existing projects"
        ])
        
        report_text = "\n".join(report_lines)
        
        # Save report
        with open("github_data_sources_report.txt", 'w') as f:
            f.write(report_text)
        
        return report_text

if __name__ == "__main__":
    finder = GitHubDataSourcesFinder()
    
    print("🔍 GitHub Data Sources Finder")
    print("=" * 40)
    print("Searching for repositories that might help collect PulseChain and HEX data...")
    print()
    
    repositories = finder.find_pulsechain_hex_sources()
    
    if repositories:
        report = finder.generate_report(repositories)
        
        print("📊 SEARCH COMPLETE!")
        print("=" * 40)
        print(f"Analyzed {len(repositories)} repositories")
        print(f"Report saved to: github_data_sources_report.txt")
        print()
        
        # Show top 5 results
        print("🏆 TOP 5 POTENTIAL DATA SOURCES:")
        print("-" * 35)
        for i, repo in enumerate(repositories[:5], 1):
            print(f"{i}. {repo['name']} (Score: {repo['potential_score']})")
            print(f"   {repo['description'][:80]}{'...' if len(repo['description']) > 80 else ''}")
            print(f"   {repo['url']}")
            print()
    else:
        print("❌ No repositories found. Check your internet connection.")
