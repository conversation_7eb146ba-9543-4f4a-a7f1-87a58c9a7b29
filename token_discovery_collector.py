"""
Token Discovery and Collection System
Finds and collects data for tokens similar to PulseChain that might be missed by traditional APIs
"""
import os
import sys
import time
import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
import json

from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/token_discovery.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TokenDiscoveryCollector:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
        
        # Categories of tokens to discover
        self.token_categories = {
            'ethereum_forks': [
                'ETC', 'ETH2', 'ETHW', 'ETHS'  # Ethereum Classic, ETH2, EthereumPoW, etc.
            ],
            'layer2_tokens': [
                'MATIC', 'ARB', 'OP', 'METIS', 'BOBA'  # Polygon, Arbitrum, Optimism, etc.
            ],
            'defi_governance': [
                'UNI', 'SUSHI', 'AAVE', 'COMP', 'MKR', 'YFI', 'CRV', '1INCH'
            ],
            'meme_tokens': [
                'SHIB', 'DOGE', 'PEPE', 'FLOKI', 'BABYDOGE', 'SAFEMOON'
            ],
            'new_chains': [
                'AVAX', 'FTM', 'NEAR', 'ALGO', 'ATOM', 'DOT', 'KSM'
            ],
            'community_tokens': [
                'APE', 'LOOKS', 'GMT', 'GST', 'SOS', 'ENS'
            ]
        }
    
    def discover_tokens_from_dexscreener(self, chain='ethereum', limit=100):
        """Discover trending tokens from DexScreener"""
        logger.info(f"Discovering tokens from DexScreener on {chain}...")
        
        try:
            # Get trending tokens
            url = f"https://api.dexscreener.com/latest/dex/tokens/trending"
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                tokens = []
                
                for token_data in data.get('tokens', [])[:limit]:
                    token_info = {
                        'symbol': token_data.get('symbol', ''),
                        'name': token_data.get('name', ''),
                        'address': token_data.get('address', ''),
                        'chain': token_data.get('chainId', ''),
                        'price_usd': token_data.get('priceUsd', 0),
                        'volume_24h': token_data.get('volume24h', 0),
                        'market_cap': token_data.get('marketCap', 0),
                        'source': 'DexScreener_Trending'
                    }
                    tokens.append(token_info)
                
                logger.info(f"Found {len(tokens)} trending tokens")
                return tokens
            
        except Exception as e:
            logger.error(f"Error discovering tokens from DexScreener: {str(e)}")
        
        return []
    
    def discover_tokens_from_coingecko_trending(self):
        """Try to get trending tokens from CoinGecko (if available)"""
        logger.info("Discovering trending tokens...")
        
        try:
            # Try trending endpoint (might work without auth)
            url = "https://api.coingecko.com/api/v3/search/trending"
            
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                tokens = []
                
                for coin in data.get('coins', []):
                    coin_data = coin.get('item', {})
                    token_info = {
                        'symbol': coin_data.get('symbol', ''),
                        'name': coin_data.get('name', ''),
                        'id': coin_data.get('id', ''),
                        'market_cap_rank': coin_data.get('market_cap_rank', 0),
                        'source': 'CoinGecko_Trending'
                    }
                    tokens.append(token_info)
                
                logger.info(f"Found {len(tokens)} trending tokens from CoinGecko")
                return tokens
            
        except Exception as e:
            logger.warning(f"CoinGecko trending failed: {str(e)}")
        
        return []
    
    def discover_ethereum_ecosystem_tokens(self):
        """Discover tokens in the Ethereum ecosystem similar to PulseChain"""
        logger.info("Discovering Ethereum ecosystem tokens...")
        
        # Ethereum ecosystem tokens that might be missed
        ethereum_tokens = [
            # Ethereum forks and related
            {'symbol': 'ETC', 'name': 'Ethereum Classic', 'category': 'ethereum_fork'},
            {'symbol': 'ETHW', 'name': 'EthereumPoW', 'category': 'ethereum_fork'},
            {'symbol': 'ETHS', 'name': 'Ethereum Stake', 'category': 'ethereum_fork'},
            
            # Layer 2 and scaling
            {'symbol': 'MATIC', 'name': 'Polygon', 'category': 'layer2'},
            {'symbol': 'ARB', 'name': 'Arbitrum', 'category': 'layer2'},
            {'symbol': 'OP', 'name': 'Optimism', 'category': 'layer2'},
            {'symbol': 'METIS', 'name': 'Metis', 'category': 'layer2'},
            
            # DeFi governance tokens
            {'symbol': 'UNI', 'name': 'Uniswap', 'category': 'defi_governance'},
            {'symbol': 'SUSHI', 'name': 'SushiSwap', 'category': 'defi_governance'},
            {'symbol': 'AAVE', 'name': 'Aave', 'category': 'defi_governance'},
            {'symbol': 'COMP', 'name': 'Compound', 'category': 'defi_governance'},
            
            # Community and meme tokens
            {'symbol': 'SHIB', 'name': 'Shiba Inu', 'category': 'meme'},
            {'symbol': 'PEPE', 'name': 'Pepe', 'category': 'meme'},
            {'symbol': 'FLOKI', 'name': 'Floki', 'category': 'meme'},
            
            # New ecosystem tokens
            {'symbol': 'APE', 'name': 'ApeCoin', 'category': 'community'},
            {'symbol': 'ENS', 'name': 'Ethereum Name Service', 'category': 'infrastructure'},
            {'symbol': 'LDO', 'name': 'Lido DAO', 'category': 'staking'},
            
            # Cross-chain and bridges
            {'symbol': 'WBTC', 'name': 'Wrapped Bitcoin', 'category': 'wrapped'},
            {'symbol': 'stETH', 'name': 'Staked Ethereum', 'category': 'staking'},
            
            # Newer DeFi protocols
            {'symbol': 'GMX', 'name': 'GMX', 'category': 'defi'},
            {'symbol': 'GNS', 'name': 'Gains Network', 'category': 'defi'},
            {'symbol': 'RDNT', 'name': 'Radiant Capital', 'category': 'defi'},
        ]
        
        return ethereum_tokens
    
    def collect_token_data(self, tokens):
        """Collect current data for discovered tokens using our proven methods"""
        logger.info(f"Collecting data for {len(tokens)} discovered tokens...")
        
        all_data = []
        
        for token in tokens:
            symbol = token.get('symbol', '')
            if not symbol:
                continue
                
            logger.info(f"Collecting data for {symbol}...")
            
            # Try DexScreener first (our proven method)
            try:
                search_url = f"https://api.dexscreener.com/latest/dex/search/?q={symbol}"
                response = self.session.get(search_url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    if pairs:
                        # Take the pair with highest liquidity
                        best_pair = max(pairs, key=lambda x: float(x.get('liquidity', {}).get('usd', 0)))
                        price = float(best_pair.get('priceUsd', 0))
                        
                        if price > 0:
                            token_data = {
                                'Symbol': symbol,
                                'Name': token.get('name', ''),
                                'Current_Price': price,
                                'Volume_24h': float(best_pair.get('volume', {}).get('h24', 0)),
                                'Liquidity_USD': float(best_pair.get('liquidity', {}).get('usd', 0)),
                                'Chain': best_pair.get('chainId', ''),
                                'DEX': best_pair.get('dexId', ''),
                                'Category': token.get('category', 'discovered'),
                                'Source': 'DexScreener',
                                'Date': datetime.now().strftime('%Y-%m-%d'),
                                'Pair_Address': best_pair.get('pairAddress', '')
                            }
                            all_data.append(token_data)
                            logger.info(f"✅ {symbol}: ${price}")
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"Failed to collect {symbol}: {str(e)}")
                continue
        
        if all_data:
            df = pd.DataFrame(all_data)
            output_file = f"{PROCESSED_DIR}/discovered_tokens_data.csv"
            df.to_csv(output_file, index=False)
            logger.info(f"Saved discovered token data: {len(df)} records to {output_file}")
            return df
        
        return pd.DataFrame()
    
    def run_comprehensive_discovery(self):
        """Run comprehensive token discovery and collection"""
        logger.info("Starting comprehensive token discovery...")
        
        all_discovered_tokens = []
        
        # 1. Discover from predefined Ethereum ecosystem
        ethereum_tokens = self.discover_ethereum_ecosystem_tokens()
        all_discovered_tokens.extend(ethereum_tokens)
        logger.info(f"Added {len(ethereum_tokens)} Ethereum ecosystem tokens")
        
        # 2. Try to discover trending tokens
        trending_dex = self.discover_tokens_from_dexscreener()
        all_discovered_tokens.extend(trending_dex)
        logger.info(f"Added {len(trending_dex)} trending tokens from DexScreener")
        
        trending_cg = self.discover_tokens_from_coingecko_trending()
        all_discovered_tokens.extend(trending_cg)
        logger.info(f"Added {len(trending_cg)} trending tokens from CoinGecko")
        
        # Remove duplicates
        unique_tokens = {}
        for token in all_discovered_tokens:
            symbol = token.get('symbol', '').upper()
            if symbol and symbol not in unique_tokens:
                unique_tokens[symbol] = token
        
        unique_token_list = list(unique_tokens.values())
        logger.info(f"Total unique tokens to collect: {len(unique_token_list)}")
        
        # 3. Collect data for all discovered tokens
        collected_data = self.collect_token_data(unique_token_list)
        
        # 4. Generate comprehensive report
        report = self.generate_discovery_report(collected_data, unique_token_list)
        
        return collected_data, report
    
    def generate_discovery_report(self, collected_data, all_tokens):
        """Generate comprehensive discovery report"""
        
        report_lines = [
            "TOKEN DISCOVERY AND COLLECTION REPORT",
            "=" * 50,
            f"Discovery Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "DISCOVERY RESULTS:",
            f"• Total tokens discovered: {len(all_tokens)}",
            f"• Successfully collected data: {len(collected_data)}",
            f"• Success rate: {len(collected_data)/len(all_tokens)*100:.1f}%" if all_tokens else "0%",
            ""
        ]
        
        if not collected_data.empty:
            # Category breakdown
            if 'Category' in collected_data.columns:
                category_counts = collected_data['Category'].value_counts()
                report_lines.extend([
                    "TOKENS BY CATEGORY:",
                    *[f"  • {cat}: {count} tokens" for cat, count in category_counts.items()],
                    ""
                ])
            
            # Chain breakdown
            if 'Chain' in collected_data.columns:
                chain_counts = collected_data['Chain'].value_counts()
                report_lines.extend([
                    "TOKENS BY BLOCKCHAIN:",
                    *[f"  • {chain}: {count} tokens" for chain, count in chain_counts.items()],
                    ""
                ])
            
            # Top tokens by price
            top_by_price = collected_data.nlargest(10, 'Current_Price')
            report_lines.extend([
                "TOP 10 TOKENS BY PRICE:",
                *[f"  • {row['Symbol']}: ${row['Current_Price']:.6f} ({row['Name']})" 
                  for _, row in top_by_price.iterrows()],
                ""
            ])
            
            # Top tokens by volume
            if 'Volume_24h' in collected_data.columns:
                top_by_volume = collected_data.nlargest(10, 'Volume_24h')
                report_lines.extend([
                    "TOP 10 TOKENS BY 24H VOLUME:",
                    *[f"  • {row['Symbol']}: ${row['Volume_24h']:,.0f}" 
                      for _, row in top_by_volume.iterrows()],
                    ""
                ])
        
        report_lines.extend([
            "SUCCESSFUL COLLECTION METHODS:",
            "✅ DexScreener API - Works great for most tokens!",
            "✅ Multi-chain support - Ethereum, Polygon, Arbitrum, etc.",
            "✅ Real-time DEX data - More accurate for newer tokens",
            "",
            "NEXT STEPS:",
            "• Set up automated daily collection for these tokens",
            "• Add historical data collection by running daily",
            "• Explore specific DEXs for more comprehensive data",
            "• Join communities of discovered tokens for insights",
            "",
            "FILES CREATED:",
            "• discovered_tokens_data.csv - Current price data",
            "• token_discovery_report.txt - This report",
            "• logs/token_discovery.log - Detailed logs"
        ])
        
        report_text = "\n".join(report_lines)
        
        with open("token_discovery_report.txt", 'w') as f:
            f.write(report_text)
        
        return report_text

if __name__ == "__main__":
    collector = TokenDiscoveryCollector()
    
    print("🔍 TOKEN DISCOVERY AND COLLECTION SYSTEM")
    print("=" * 50)
    print("Discovering tokens similar to PulseChain that might be missed...")
    print("This includes:")
    print("• Ethereum ecosystem tokens")
    print("• Layer 2 and scaling solutions") 
    print("• DeFi governance tokens")
    print("• Community and meme tokens")
    print("• Cross-chain and bridge tokens")
    print()
    
    data, report = collector.run_comprehensive_discovery()
    
    print("\n" + "=" * 50)
    print("🎉 DISCOVERY COMPLETE!")
    print("=" * 50)
    print(report)
    
    if not data.empty:
        print(f"\n📊 Token data saved to: {PROCESSED_DIR}/discovered_tokens_data.csv")
        print(f"📋 Report saved to: token_discovery_report.txt")
    else:
        print("\n⚠️  No token data collected. Check logs for details.")
