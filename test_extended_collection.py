#!/usr/bin/env python3
"""
Test script for the Extended Historical Cryptocurrency Data Collection System
Tests individual components and provides a quick demo
"""

import os
import sys
import pandas as pd
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """Test the extended configuration"""
    print("Testing Extended Configuration...")

    try:
        import extended_config as config
        
        print(f"✓ Date Range: {config.EXTENDED_START_DATE.strftime('%Y-%m-%d')} to {config.END_DATE.strftime('%Y-%m-%d')}")
        print(f"✓ Total Years: {(config.END_DATE - config.EXTENDED_START_DATE).days / 365.25:.1f} years")
        print(f"✓ Early Data Sources: {len(config.EARLY_DATA_SOURCES)} configured")
        print(f"✓ Early Altcoins: {len(config.EARLY_ALTCOINS)} configured")
        print(f"✓ GitHub Sources: {len(config.GITHUB_DATA_SOURCES)} configured")
        print(f"✓ Kaggle Datasets: {len(config.KAGGLE_DATASETS)} configured")
        
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {str(e)}")
        return False

def test_extended_collector():
    """Test the extended historical collector"""
    print("\nTesting Extended Historical Collector...")
    
    try:
        from extended_historical_collector import ExtendedHistoricalCollector
        
        collector = ExtendedHistoricalCollector()
        print(f"✓ Collector initialized")
        print(f"✓ Data directory: {collector.data_dir}")
        print(f"✓ Extended directory: {collector.extended_dir}")
        print(f"✓ Date range: {collector.start_date} to {collector.end_date}")
        
        return True
    except Exception as e:
        print(f"✗ Extended collector test failed: {str(e)}")
        return False

def test_comprehensive_merger():
    """Test the comprehensive historical merger"""
    print("\nTesting Comprehensive Historical Merger...")
    
    try:
        from comprehensive_historical_merger import ComprehensiveHistoricalMerger
        
        merger = ComprehensiveHistoricalMerger()
        print(f"✓ Merger initialized")
        print(f"✓ Data directory: {merger.data_dir}")
        print(f"✓ Output directory: {merger.output_dir}")
        print(f"✓ Date ranges configured")
        
        return True
    except Exception as e:
        print(f"✗ Comprehensive merger test failed: {str(e)}")
        return False

def test_orchestrator():
    """Test the extended collection orchestrator"""
    print("\nTesting Extended Collection Orchestrator...")
    
    try:
        from run_extended_collection import ExtendedCollectionOrchestrator
        
        orchestrator = ExtendedCollectionOrchestrator()
        print(f"✓ Orchestrator initialized")
        print(f"✓ Collector and merger components loaded")
        
        return True
    except Exception as e:
        print(f"✗ Orchestrator test failed: {str(e)}")
        return False

def test_directory_structure():
    """Test that all required directories exist or can be created"""
    print("\nTesting Directory Structure...")
    
    try:
        import extended_config as config
        
        for name, path in config.EXTENDED_DATA_PATHS.items():
            os.makedirs(path, exist_ok=True)
            if os.path.exists(path):
                print(f"✓ {name}: {path}")
            else:
                print(f"✗ {name}: Failed to create {path}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Directory structure test failed: {str(e)}")
        return False

def test_dependencies():
    """Test that all required dependencies are available"""
    print("\nTesting Dependencies...")
    
    required_packages = [
        'pandas', 'requests', 'beautifulsoup4', 'tqdm', 
        'numpy', 'datetime', 'logging', 'json', 'csv', 'gzip'
    ]
    
    optional_packages = [
        'kaggle', 'yfinance', 'cryptocmd', 'pycoingecko'
    ]
    
    all_available = True
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (REQUIRED)")
            all_available = False
    
    for package in optional_packages:
        try:
            __import__(package)
            print(f"✓ {package} (optional)")
        except ImportError:
            print(f"⚠ {package} (optional - some features may not work)")
    
    return all_available

def demo_data_sources():
    """Demonstrate the available data sources"""
    print("\nData Sources Demo...")
    
    try:
        import extended_config as config
        
        print("\n📊 EARLY DATA SOURCES (2011-2013):")
        print("-" * 40)
        
        for source_name, source_config in config.EARLY_DATA_SOURCES.items():
            print(f"\n{source_name.upper()}:")
            if 'exchanges' in source_config:
                for exchange in source_config['exchanges']:
                    print(f"  • {exchange}")
            elif 'chart_types' in source_config:
                for chart in source_config['chart_types']:
                    print(f"  • {chart}")
            elif 'cmc_snapshots' in source_config:
                print(f"  • {len(source_config['cmc_snapshots'])} historical snapshots")

        print(f"\n🪙 EARLY ALTCOINS ({len(config.EARLY_ALTCOINS)} coins):")
        print("-" * 30)
        for symbol, info in config.EARLY_ALTCOINS.items():
            launch_year = info['launch_date'].year
            print(f"  • {symbol} ({info['name']}) - {launch_year}")

        print(f"\n🐙 GITHUB REPOSITORIES ({len(config.GITHUB_DATA_SOURCES)} sources):")
        print("-" * 35)
        for source in config.GITHUB_DATA_SOURCES:
            print(f"  • {source['name']}: {source['description']}")

        print(f"\n📈 KAGGLE DATASETS ({len(config.KAGGLE_DATASETS)} datasets):")
        print("-" * 30)
        for dataset in config.KAGGLE_DATASETS:
            priority = dataset['priority'].upper()
            print(f"  • {dataset['name']} ({priority})")
        
        return True
    except Exception as e:
        print(f"✗ Demo failed: {str(e)}")
        return False

def show_expected_results():
    """Show what results to expect from the extended collection"""
    print("\n🎯 EXPECTED RESULTS:")
    print("=" * 50)
    
    print("\nDATA COVERAGE:")
    print("• 12-13 years of historical data (2011-2024)")
    print("• Bitcoin complete trading history from early exchanges")
    print("• Early altcoins (Litecoin, Namecoin, Peercoin, XRP, Dogecoin)")
    print("• CoinMarketCap historical snapshots from 2013 launch")
    print("• Comprehensive dataset with 100,000+ records")
    
    print("\nFILE SIZES:")
    print("• Extended Historical Data: ~500 MB - 2 GB")
    print("• Bitcoin Early Data: ~100-200 MB")
    print("• Early Altcoins: ~50-100 MB")
    print("• Combined Dataset: ~1-3 GB")
    
    print("\nOUTPUT FILES:")
    print("• comprehensive_12_year_crypto_dataset.csv")
    print("• comprehensive_historical_analysis.txt")
    print("• Individual source files for each data source")
    print("• Detailed collection and validation reports")
    
    print("\nTIME ESTIMATES:")
    print("• Full collection: 2-6 hours (depending on connection)")
    print("• Individual sources: 15-60 minutes each")
    print("• Data merging and validation: 10-30 minutes")

def main():
    """Run all tests and demos"""
    print("Extended Historical Cryptocurrency Data Collection")
    print("SYSTEM TEST & DEMO")
    print("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Dependencies", test_dependencies),
        ("Directory Structure", test_directory_structure),
        ("Extended Collector", test_extended_collector),
        ("Comprehensive Merger", test_comprehensive_merger),
        ("Orchestrator", test_orchestrator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} test failed!")
    
    print(f"\n{'='*60}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! System is ready for extended collection.")
        
        # Show demo and expected results
        demo_data_sources()
        show_expected_results()
        
        print(f"\n{'='*60}")
        print("🚀 READY TO START EXTENDED COLLECTION!")
        print("Run: python run_extended_collection.py")
        print("=" * 60)
        
    else:
        print("❌ Some tests failed. Please fix issues before running collection.")
        print("\nTo install missing dependencies:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
