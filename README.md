# Historical Cryptocurrency Data Collector

A comprehensive Python toolkit for collecting historical cryptocurrency data with a focus on **new listings detection**. This project provides free access to 5+ years of cryptocurrency market data by leveraging CoinMarketCap's historical snapshots and CoinGecko's API.

## 🎯 Project Overview

This tool helps researchers and analysts collect historical cryptocurrency data focusing on new listings from October 2020 to October 2024. It automatically downloads weekly market snapshots, detects newly listed cryptocurrencies, and provides comprehensive data analysis.

### Key Features

- **Free Data Collection**: No API keys or paid subscriptions required
- **Historical Coverage**: 5+ years of data (October 2020 - October 2024)
- **New Listings Detection**: Automatically identifies newly listed cryptocurrencies
- **Multiple Data Sources**: CoinMarketCap historical snapshots + CoinGecko API
- **Data Validation**: Built-in quality checks and validation
- **Export Ready**: Clean CSV outputs for research and analysis

## 📊 Data Sources

### Primary: CoinMarketCap Historical Snapshots
- **Source**: coinmarketcap.com/historical/
- **Coverage**: Weekly snapshots from 2013 to present
- **Content**: Top ~500-2000 coins by market cap per snapshot
- **Format**: CSV files with price, market cap, volume, rank data
- **Cost**: Completely free, no registration required

### Secondary: CoinGecko API  
- **Source**: CoinGecko's free API tier
- **Coverage**: Recent listings and trending coins
- **Rate Limit**: 10-50 calls/minute (free tier)
- **Purpose**: Supplement historical data with recent listings

## 📈 Expected Data Volume

- **Raw Snapshots**: ~130 MB (260 weekly files, ~0.5 MB each)
- **Processed New Listings**: ~2-3 MB (5,000-15,000 new coins)
- **Download Time**: 10-30 minutes (depending on connection)
- **Storage**: Easily fits on any modern device

## 🚀 Quick Start

### 1. Setup
```bash
# Clone or download the project
git clone <repository-url>
cd historical-crypto-data

# Run setup (installs dependencies and creates directories)
python setup.py
```

### 2. Collect Data
```bash
# Run complete data collection (recommended)
python main.py

# Or run specific components:
python main.py --download-only     # Only download snapshots
python main.py --detect-only       # Only detect new listings
python main.py --coingecko-only    # Only fetch CoinGecko data
python main.py --validate-only     # Only run data validation
```

### 3. Access Results
Check the `data/processed/` directory for:
- `new_crypto_listings_2020-2024.csv` - Detected new listings
- `merged_crypto_listings.csv` - Combined data from all sources
- `full_historical_data.csv` - Complete historical dataset
- `final_report.txt` - Summary report

## 📁 Project Structure

```
historical-crypto-data/
├── main.py                 # Main orchestrator script
├── cmc_downloader.py       # CoinMarketCap data downloader
├── new_listings_detector.py # New listings detection algorithm
├── coingecko_fetcher.py    # CoinGecko API integration
├── data_validator.py       # Data validation and quality checks
├── config.py              # Configuration settings
├── setup.py               # Setup and installation script
├── requirements.txt       # Python dependencies
├── data/                  # Data directory
│   ├── snapshots/         # Raw CMC snapshots
│   └── processed/         # Processed data and results
└── logs/                  # Log files
```

## 🔧 Configuration

Edit `config.py` to customize:
- Date ranges (START_DATE, END_DATE)
- Rate limiting settings
- Output file paths
- API delays and retry settings

## 📋 Requirements

- Python 3.7+
- Internet connection
- ~200 MB free disk space
- Dependencies (auto-installed by setup.py):
  - requests
  - pandas
  - beautifulsoup4
  - pycoingecko
  - tqdm
  - python-dateutil
  - numpy

## 🔍 Data Validation

The tool includes comprehensive validation:
- File integrity checks
- Data completeness validation
- Date consistency verification
- Duplicate detection
- Quality metrics reporting

Run validation separately:
```bash
python main.py --validate-only
```

## 📊 Output Data Format

### New Listings CSV
```csv
first_seen_date,symbol,name,first_seen_price,first_seen_market_cap,first_seen_rank
2021-03-15,EXAMPLE,Example Coin,0.50,1000000,150
```

### Full Historical Data
```csv
snapshot_date,symbol,name,price,market_cap,volume_24h,rank
2021-03-15,BTC,Bitcoin,50000,900000000000,30000000000,1
```

## ⚠️ Important Notes

1. **Rate Limiting**: The tool includes built-in delays to respect API limits
2. **Data Accuracy**: New listings are detected by comparing consecutive snapshots
3. **Coverage**: Focuses on coins that appeared in CMC's top rankings
4. **Updates**: Historical snapshots are static; recent data comes from CoinGecko

## 🐛 Troubleshooting

### Common Issues

**Download Failures**
- Check internet connection
- Verify CoinMarketCap accessibility
- Review logs in `logs/` directory

**Empty Results**
- Ensure snapshots directory has CSV files
- Check date range in config.py
- Run validation to identify issues

**API Errors**
- CoinGecko rate limits: Tool includes delays
- Network timeouts: Increase retry settings in config.py

### Getting Help

1. Check log files in `logs/` directory
2. Run validation: `python main.py --validate-only`
3. Review error messages in console output

## 📄 License

This project is for research and educational purposes. Please respect the terms of service of data providers (CoinMarketCap, CoinGecko).

## 🤝 Contributing

Contributions welcome! Areas for improvement:
- Additional data sources
- Enhanced validation
- Performance optimizations
- Better error handling

---

**Estimated Results**: 5,000-15,000 new cryptocurrency listings from October 2020 to October 2024, with comprehensive market data for research and analysis.
