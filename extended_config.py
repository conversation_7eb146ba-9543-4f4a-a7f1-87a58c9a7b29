"""
Extended Configuration settings for comprehensive crypto data collection
Supports 12-13 years of historical data collection (2011-2024)
"""
from datetime import datetime, timedelta

# Extended Data collection settings for 12-13 years
EXTENDED_START_DATE = datetime(2011, 1, 1)    # Bitcoin's early trading days
ORIGINAL_START_DATE = datetime(2020, 10, 1)   # Original 5-year range start
END_DATE = datetime(2024, 10, 3)              # Current end date

# Historical periods for analysis
HISTORICAL_PERIODS = {
    'early_era': {
        'name': 'Early Era (2011-2013)',
        'start': datetime(2011, 1, 1),
        'end': datetime(2013, 12, 31),
        'description': 'Bitcoin dominance, first altcoins'
    },
    'growth_era': {
        'name': 'Growth Era (2014-2017)', 
        'start': datetime(2014, 1, 1),
        'end': datetime(2017, 12, 31),
        'description': 'Altcoin emergence, ICO boom'
    },
    'boom_era': {
        'name': 'Boom Era (2018-2020)',
        'start': datetime(2018, 1, 1), 
        'end': datetime(2020, 12, 31),
        'description': 'Market maturation, institutional adoption'
    },
    'modern_era': {
        'name': 'Modern Era (2021-2024)',
        'start': datetime(2021, 1, 1),
        'end': datetime(2024, 12, 31),
        'description': 'DeFi, NFTs, mainstream adoption'
    }
}

# Early cryptocurrency data sources (2011-2013)
EARLY_DATA_SOURCES = {
    'bitcoincharts': {
        'base_url': 'http://api.bitcoincharts.com/v1/csv/',
        'exchanges': [
            'bitstampUSD',   # Available from 2011
            'mtgoxUSD',      # Historical data (defunct)
            'btceUSD',       # Historical data (defunct) 
            'coinbaseUSD'    # Available from 2012
        ],
        'format': 'csv.gz',
        'rate_limit': 2  # seconds between requests
    },
    'blockchain_info': {
        'base_url': 'https://api.blockchain.info/charts/',
        'chart_types': [
            'market-price',      # Bitcoin price
            'market-cap',        # Market capitalization
            'trade-volume',      # Trading volume
            'n-transactions',    # Number of transactions
            'difficulty',        # Mining difficulty
            'hash-rate'          # Network hash rate
        ],
        'rate_limit': 2
    },
    'wayback_machine': {
        'base_url': 'https://web.archive.org/web/',
        'cmc_snapshots': [
            '20130428',  # CMC launch (April 28, 2013)
            '20130630',  # Q2 2013
            '20130930',  # Q3 2013
            '20131231',  # End of 2013
            '20140331',  # Q1 2014
            '20140630',  # Q2 2014
            '20140930',  # Q3 2014
            '20141231',  # End of 2014
            '20150331',  # Q1 2015
            '20150630',  # Q2 2015
        ],
        'rate_limit': 5  # Be respectful to Wayback Machine
    }
}

# Early altcoins that existed in 2011-2013
EARLY_ALTCOINS = {
    'LTC': {
        'name': 'Litecoin',
        'launch_date': datetime(2011, 10, 7),
        'description': 'Silver to Bitcoin\'s gold'
    },
    'NMC': {
        'name': 'Namecoin', 
        'launch_date': datetime(2011, 4, 18),
        'description': 'First Bitcoin fork, decentralized DNS'
    },
    'PPC': {
        'name': 'Peercoin',
        'launch_date': datetime(2012, 8, 19), 
        'description': 'First proof-of-stake cryptocurrency'
    },
    'XRP': {
        'name': 'Ripple',
        'launch_date': datetime(2012, 6, 1),
        'description': 'Banking-focused cryptocurrency'
    },
    'DOGE': {
        'name': 'Dogecoin',
        'launch_date': datetime(2013, 12, 6),
        'description': 'Meme-based cryptocurrency'
    },
    'NXT': {
        'name': 'Nxt',
        'launch_date': datetime(2013, 11, 24),
        'description': 'Pure proof-of-stake platform'
    }
}

# GitHub repositories with historical crypto data
GITHUB_DATA_SOURCES = [
    {
        'name': 'crypto2',
        'url': 'https://raw.githubusercontent.com/sstoeckl/crypto2/main/data/crypto_history.csv',
        'description': 'Comprehensive crypto market data',
        'format': 'csv'
    },
    {
        'name': 'bitcoin_data',
        'url': 'https://raw.githubusercontent.com/datasets/bitcoin/master/data/bitcoin.csv',
        'description': 'Bitcoin price data',
        'format': 'csv'
    },
    {
        'name': 'cryptocurrency_data',
        'url': 'https://raw.githubusercontent.com/datasets/cryptocurrency-historical-prices/master/data/all.csv',
        'description': 'Historical cryptocurrency prices',
        'format': 'csv'
    }
]

# Kaggle datasets for historical crypto data
KAGGLE_DATASETS = [
    {
        'name': 'mczielinski/bitcoin-historical-data',
        'description': 'Bitcoin data at 1-min intervals from select exchanges, Jan 2012 to Present',
        'priority': 'high'
    },
    {
        'name': 'bizzyvinci/coinmarketcap-historical-data', 
        'description': 'CoinMarketCap historical snapshots 2013-2023',
        'priority': 'high'
    },
    {
        'name': 'sudalairajkumar/cryptocurrencypricehistory',
        'description': 'Cryptocurrency price history',
        'priority': 'medium'
    },
    {
        'name': 'maharshipandya/-cryptocurrency-historical-prices-dataset',
        'description': 'Comprehensive cryptocurrency historical prices',
        'priority': 'medium'
    }
]

# Bulk Data Sources (newly added)
BULK_DATA_SOURCES = {
    'cryptodatadownload': {
        'base_url': 'https://www.cryptodatadownload.com/cdd/',
        'description': 'Daily historical data from various exchanges (e.g., Binance, Kraken)',
        'exchanges': ['Binance', 'Kraken'], # Example exchanges, can be expanded
        'pairs': ['BTCUSDT', 'ETHUSDT'], # Example pairs, can be expanded
        'interval': 'd', # daily
        'rate_limit': 1 # seconds
    },
    'github_bulk_downloader': { # Placeholder for the martkir/crypto-prices-download script
        'script_url': 'https://raw.githubusercontent.com/martkir/crypto-prices-download/main/download_prices.py',
        'description': 'Script to download OHLC data for 1926 tokens from Syve.ai',
        'rate_limit': 5 # seconds, be respectful
    }
}

# Extended CoinMarketCap settings
CMC_EXTENDED_SETTINGS = {
    'base_url': 'https://coinmarketcap.com/historical/',
    'export_url': 'https://web-api.coinmarketcap.com/v1/cryptocurrency/listings/historical',
    'earliest_snapshot': datetime(2013, 4, 28),  # CMC launch date
    'snapshot_frequency': 'weekly',  # weekly snapshots
    'max_coins_per_snapshot': 2000
}

# Rate limiting settings for extended collection
EXTENDED_RATE_LIMITS = {
    'bitcoincharts': 2,      # seconds between requests
    'blockchain_info': 2,    # seconds between requests  
    'cryptocompare': 1.5,    # free tier limit
    'wayback_machine': 5,    # be respectful
    'github': 1,             # general rate limiting
    'kaggle': 0.5,           # if using API
    'cryptodatadownload': 1, # for bulk downloads
    'github_bulk_downloader': 5, # for the script
    'default': 2             # default delay
}

# File paths for extended collection
EXTENDED_DATA_PATHS = {
    'base_dir': 'data',
    'extended_dir': 'data/extended_historical',
    'comprehensive_dir': 'data/comprehensive', 
    'processed_dir': 'data/processed',
    'logs_dir': 'logs',
    'temp_dir': 'data/temp',
    'bulk_dir': 'data/bulk' # New directory for bulk data
}

# Output file configurations
EXTENDED_OUTPUT_FILES = {
    'bitcoincharts_data': 'bitcoincharts_historical_data.csv',
    'early_altcoins_data': 'early_altcoins_historical_data.csv',
    'kaggle_bitcoin_data': 'kaggle_bitcoin_historical_data.csv',
    'github_datasets_data': 'github_datasets_historical_data.csv',
    'wayback_cmc_data': 'wayback_cmc_historical_data.csv',
    'blockchain_info_data': 'blockchain_info_historical_data.csv',
    'cryptodatadownload_data': 'cryptodatadownload_historical_data.csv', # New bulk data file
    'github_bulk_data': 'github_bulk_historical_data.csv', # New bulk data file
    'extended_combined': 'extended_historical_combined_data.csv',
    'comprehensive_dataset': 'comprehensive_12_year_crypto_dataset.csv',
    'comprehensive_analysis': 'comprehensive_historical_analysis.txt',
    'collection_report': 'extended_historical_collection_report.txt'
}

# Data validation settings
VALIDATION_SETTINGS = {
    'min_records_per_source': 100,
    'max_price_change_percent': 1000,  # 1000% daily change threshold
    'required_columns': ['date', 'symbol', 'price'],
    'date_format_tolerance': True,
    'allow_missing_data': True,
    'duplicate_threshold': 0.05  # 5% duplicates allowed
}

# Collection priorities (high, medium, low)
COLLECTION_PRIORITIES = {
    'bitcoincharts': 'high',      # Essential for early Bitcoin data
    'blockchain_info': 'high',    # Reliable Bitcoin metrics
    'early_altcoins': 'high',     # Important historical context
    'kaggle_bitcoin': 'high',     # Comprehensive Bitcoin dataset
    'wayback_cmc': 'medium',      # Historical but may be incomplete
    'github_datasets': 'medium',  # Supplementary data
    'existing_data': 'high'       # Current 2020-2024 data
}

# Memory and performance settings
PERFORMANCE_SETTINGS = {
    'chunk_size': 10000,          # Process data in chunks
    'max_memory_usage_gb': 4,     # Maximum memory usage
    'parallel_downloads': 3,      # Number of parallel downloads
    'cache_intermediate': True,   # Cache intermediate results
    'compress_output': False      # Compress large output files
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_rotation': True,
    'max_file_size_mb': 10,
    'backup_count': 5
}

# Feature flags for extended collection
FEATURE_FLAGS = {
    'enable_wayback_machine': True,
    'enable_github_sources': True, 
    'enable_kaggle_integration': True,
    'enable_early_altcoins': True,
    'enable_blockchain_info': True,
    'enable_data_validation': True,
    'enable_duplicate_removal': True,
    'enable_comprehensive_analysis': True
}

# Export settings
EXPORT_SETTINGS = {
    'include_metadata': True,
    'standardize_columns': True,
    'remove_duplicates': True,
    'sort_by_date': True,
    'add_data_quality_flags': True,
    'generate_summary_stats': True
}
