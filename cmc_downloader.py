"""
CoinMarketCap Historical Data Downloader
Downloads weekly snapshots from CoinMarketCap historical data
"""
import os
import time
import requests
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm
import logging
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/cmc_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CMCDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Create directories if they don't exist
        os.makedirs(SNAPSHOTS_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def get_snapshot_dates(self):
        """Generate list of Sunday dates for weekly snapshots"""
        dates = []
        current_date = START_DATE
        
        while current_date <= END_DATE:
            dates.append(current_date)
            current_date += timedelta(weeks=1)
        
        return dates
    
    def download_snapshot(self, date):
        """Download a single snapshot for the given date"""
        date_str = date.strftime("%Y%m%d")
        filename = f"{SNAPSHOTS_DIR}/{date_str}.csv"
        
        # Skip if file already exists
        if os.path.exists(filename):
            logger.info(f"Snapshot {date_str} already exists, skipping")
            return True
        
        # Try different URL formats for CMC historical data
        urls_to_try = [
            f"https://coinmarketcap.com/historical/{date_str}/",
            f"https://web-api.coinmarketcap.com/v1/cryptocurrency/listings/historical?date={date_str}&limit=2000"
        ]
        
        for attempt in range(MAX_RETRIES):
            try:
                # First try to get the webpage and find download link
                response = self.session.get(f"https://coinmarketcap.com/historical/{date_str}/")
                
                if response.status_code == 200:
                    # Look for CSV download link in the page
                    if 'Download CSV' in response.text or 'export' in response.text:
                        # Try to download CSV directly
                        csv_url = f"https://coinmarketcap.com/historical/{date_str}/export/"
                        csv_response = self.session.get(csv_url)
                        
                        if csv_response.status_code == 200 and len(csv_response.content) > 100:
                            with open(filename, 'wb') as f:
                                f.write(csv_response.content)
                            logger.info(f"Successfully downloaded snapshot for {date_str}")
                            return True
                
                # If direct download fails, try alternative approach
                # Parse the HTML table data as fallback
                if response.status_code == 200:
                    self._parse_html_table(response.text, filename, date_str)
                    return True
                    
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed for {date_str}: {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(REQUEST_DELAY * (attempt + 1))
        
        logger.error(f"Failed to download snapshot for {date_str} after {MAX_RETRIES} attempts")
        return False
    
    def _parse_html_table(self, html_content, filename, date_str):
        """Parse HTML table data as fallback when CSV download fails"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find the main data table
            table = soup.find('table')
            if not table:
                logger.warning(f"No table found in HTML for {date_str}")
                return False
            
            # Extract table data
            rows = []
            headers = []
            
            # Get headers
            header_row = table.find('thead')
            if header_row:
                headers = [th.get_text(strip=True) for th in header_row.find_all('th')]
            
            # Get data rows
            tbody = table.find('tbody')
            if tbody:
                for row in tbody.find_all('tr'):
                    cells = [td.get_text(strip=True) for td in row.find_all('td')]
                    if cells:
                        rows.append(cells)
            
            # Create DataFrame and save
            if rows and headers:
                df = pd.DataFrame(rows, columns=headers[:len(rows[0])])
                df.to_csv(filename, index=False)
                logger.info(f"Parsed HTML table data for {date_str}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to parse HTML table for {date_str}: {str(e)}")
            return False
    
    def download_all_snapshots(self):
        """Download all historical snapshots"""
        dates = self.get_snapshot_dates()
        logger.info(f"Starting download of {len(dates)} snapshots from {START_DATE} to {END_DATE}")
        
        successful_downloads = 0
        failed_downloads = []
        
        for date in tqdm(dates, desc="Downloading snapshots"):
            if self.download_snapshot(date):
                successful_downloads += 1
            else:
                failed_downloads.append(date.strftime("%Y%m%d"))
            
            # Rate limiting
            time.sleep(REQUEST_DELAY)
        
        logger.info(f"Download complete: {successful_downloads}/{len(dates)} successful")
        if failed_downloads:
            logger.warning(f"Failed downloads: {failed_downloads}")
        
        return successful_downloads, failed_downloads

if __name__ == "__main__":
    downloader = CMCDownloader()
    successful, failed = downloader.download_all_snapshots()
    
    print(f"\nDownload Summary:")
    print(f"Successful: {successful}")
    print(f"Failed: {len(failed)}")
    if failed:
        print(f"Failed dates: {failed}")
