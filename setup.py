#!/usr/bin/env python3
"""
Setup script for Historical Crypto Data Collector
"""
import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required")
        return False
    print(f"✓ Python {sys.version.split()[0]} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "data",
        "data/snapshots", 
        "data/processed",
        "logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True

def check_internet_connection():
    """Check if internet connection is available"""
    try:
        import requests
        response = requests.get("https://httpbin.org/status/200", timeout=5)
        if response.status_code == 200:
            print("✓ Internet connection available")
            return True
    except:
        pass
    
    print("⚠ Warning: Internet connection may not be available")
    return False

def run_validation_test():
    """Run a quick validation test"""
    try:
        from data_validator import DataValidator
        validator = DataValidator()
        print("✓ Data validator imported successfully")
        return True
    except ImportError as e:
        print(f"Error importing data validator: {e}")
        return False

def main():
    print("Historical Crypto Data Collector - Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Check internet connection
    check_internet_connection()
    
    # Run validation test
    if not run_validation_test():
        print("Warning: Validation test failed, but setup can continue")
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run 'python main.py' to start full data collection")
    print("2. Or run 'python main.py --help' to see available options")
    print("3. Check the 'data/' directory for downloaded files")
    print("4. Check the 'logs/' directory for detailed logs")
    
    print(f"\nEstimated data size: ~130 MB for 5 years of snapshots")
    print(f"Estimated download time: 10-30 minutes depending on connection")

if __name__ == "__main__":
    main()
