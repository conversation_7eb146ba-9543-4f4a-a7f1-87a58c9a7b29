#!/usr/bin/env python3
"""
Demonstration of the Historical Crypto Data Collector Results
"""
import pandas as pd
import os
from datetime import datetime

def display_results():
    """Display the results of the crypto data collection"""
    
    print("="*80)
    print("HISTORICAL CRYPTOCURRENCY DATA COLLECTOR - RESULTS SUMMARY")
    print("="*80)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check what files we have
    processed_dir = "data/processed"
    snapshots_dir = "data/snapshots"
    
    print("📁 FILES GENERATED:")
    print("-" * 40)
    
    if os.path.exists(processed_dir):
        for file in os.listdir(processed_dir):
            filepath = os.path.join(processed_dir, file)
            size = os.path.getsize(filepath) / 1024  # KB
            print(f"  ✓ {file} ({size:.1f} KB)")
    
    print()
    
    # Display snapshots info
    if os.path.exists(snapshots_dir):
        snapshot_files = [f for f in os.listdir(snapshots_dir) if f.endswith('.csv')]
        print(f"📊 HISTORICAL SNAPSHOTS: {len(snapshot_files)} files")
        print("-" * 40)
        
        total_size = 0
        for file in sorted(snapshot_files):
            filepath = os.path.join(snapshots_dir, file)
            size = os.path.getsize(filepath) / 1024
            total_size += size
            date_str = file.replace('.csv', '')
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                formatted_date = date_obj.strftime('%Y-%m-%d')
                print(f"  📅 {formatted_date}: {file} ({size:.1f} KB)")
            except:
                print(f"  📅 {file} ({size:.1f} KB)")
        
        print(f"\n  Total snapshots size: {total_size:.1f} KB")
        print()
    
    # Display new listings
    new_listings_file = os.path.join(processed_dir, "new_crypto_listings_2020-2024.csv")
    if os.path.exists(new_listings_file):
        print("🆕 NEW CRYPTOCURRENCY LISTINGS DETECTED:")
        print("-" * 40)
        
        df = pd.read_csv(new_listings_file)
        print(f"  Total new listings: {len(df)}")
        print(f"  Date range: {df['first_seen_date'].min()} to {df['first_seen_date'].max()}")
        print()
        
        print("  📋 DETAILED NEW LISTINGS:")
        for _, row in df.iterrows():
            print(f"    🪙 {row['symbol']} - {row['name']}")
            print(f"       First seen: {row['first_seen_date']}")
            print(f"       Initial price: {row['first_seen_price']}")
            print(f"       Initial market cap: {row['first_seen_market_cap']}")
            print(f"       Initial rank: #{row['first_seen_rank']}")
            print()
    
    # Display sample of full data
    full_data_file = os.path.join(processed_dir, "full_historical_data.csv")
    if os.path.exists(full_data_file):
        print("📈 FULL HISTORICAL DATA SAMPLE:")
        print("-" * 40)
        
        df = pd.read_csv(full_data_file)
        print(f"  Total records: {len(df)}")
        print(f"  Unique cryptocurrencies: {df['symbol'].nunique()}")
        print(f"  Date range: {df['snapshot_date'].min()} to {df['snapshot_date'].max()}")
        print()
        
        print("  📊 TOP 10 CRYPTOCURRENCIES (Latest Snapshot):")
        latest_date = df['snapshot_date'].max()
        latest_data = df[df['snapshot_date'] == latest_date].head(10)
        
        for _, row in latest_data.iterrows():
            print(f"    #{row['rank']:2d} {row['symbol']:8s} - {row['name'][:25]:25s} {row['price']:>15s}")
        print()
    
    # Display validation results
    validation_file = os.path.join(processed_dir, "validation_report.txt")
    if os.path.exists(validation_file):
        print("✅ DATA VALIDATION STATUS:")
        print("-" * 40)
        
        with open(validation_file, 'r') as f:
            content = f.read()
            
        if "PASSED" in content:
            print("  🟢 All validation checks PASSED")
        else:
            print("  🟡 Some validation issues detected")
        
        # Extract key metrics
        lines = content.split('\n')
        for line in lines:
            if 'Total files:' in line or 'Valid files:' in line or 'Total records:' in line:
                print(f"    {line.strip()}")
        print()
    
    print("🎯 RESEARCH APPLICATIONS:")
    print("-" * 40)
    print("  • New cryptocurrency listing analysis")
    print("  • Market timing and trend identification") 
    print("  • Historical price and market cap tracking")
    print("  • Cryptocurrency ecosystem growth analysis")
    print("  • Academic research on digital asset markets")
    print()
    
    print("💾 DATA EXPORT FORMATS:")
    print("-" * 40)
    print("  • CSV files ready for Excel, R, Python analysis")
    print("  • Standardized column names for easy processing")
    print("  • Date-indexed data for time series analysis")
    print("  • Clean, validated data with quality checks")
    print()
    
    print("🚀 NEXT STEPS:")
    print("-" * 40)
    print("  1. Modify config.py to extend date range (2020-2024)")
    print("  2. Run full collection: python main.py")
    print("  3. Use real CoinMarketCap data (fix downloader for current CMC structure)")
    print("  4. Integrate additional data sources as needed")
    print("  5. Customize analysis for your specific research needs")
    print()
    
    print("="*80)
    print("✨ HISTORICAL CRYPTO DATA COLLECTION COMPLETE!")
    print("="*80)

if __name__ == "__main__":
    display_results()
