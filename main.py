"""
Main script to orchestrate historical cryptocurrency data collection
"""
import os
import sys
import argparse
import logging
from datetime import datetime
import pandas as pd

from cmc_downloader import CMCDownloader
from new_listings_detector import NewListingsDetector
from coingecko_fetcher import CoinGeckoFetcher
from data_validator import DataValidator
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CryptoDataCollector:
    def __init__(self):
        self.cmc_downloader = CMCDownloader()
        self.listings_detector = NewListingsDetector()
        self.coingecko_fetcher = CoinGeckoFetcher()
        self.data_validator = DataValidator()

        # Create all necessary directories
        os.makedirs(DATA_DIR, exist_ok=True)
        os.makedirs(SNAPSHOTS_DIR, exist_ok=True)
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def download_historical_data(self):
        """Download historical snapshots from CoinMarketCap"""
        logger.info("Starting historical data download...")
        
        successful, failed = self.cmc_downloader.download_all_snapshots()
        
        logger.info(f"Historical data download complete: {successful} successful, {len(failed)} failed")
        return successful > 0
    
    def detect_new_listings(self):
        """Detect new cryptocurrency listings from historical data"""
        logger.info("Starting new listings detection...")
        
        new_listings_df, full_data_df = self.listings_detector.detect_new_listings()
        
        if new_listings_df is not None and not new_listings_df.empty:
            # Save results
            self.listings_detector.save_results(new_listings_df, full_data_df)
            
            # Generate summary report
            summary = self.listings_detector.generate_summary_report(new_listings_df)
            logger.info("New listings detection complete")
            print(summary)
            
            return new_listings_df, full_data_df
        else:
            logger.warning("No new listings detected or detection failed")
            return None, None
    
    def fetch_recent_data(self):
        """Fetch recent data from CoinGecko"""
        logger.info("Starting recent data fetch from CoinGecko...")
        
        recent_data = self.coingecko_fetcher.fetch_and_save_recent_data()
        
        if not recent_data.empty:
            logger.info(f"Recent data fetch complete: {len(recent_data)} coins")
            return recent_data
        else:
            logger.warning("No recent data retrieved from CoinGecko")
            return pd.DataFrame()
    
    def merge_and_process_data(self, new_listings_df, recent_data_df):
        """Merge and process all collected data"""
        logger.info("Merging and processing all collected data...")
        
        merged_data = []
        
        # Add historical new listings
        if new_listings_df is not None and not new_listings_df.empty:
            historical_data = new_listings_df.copy()
            historical_data['data_source'] = 'cmc_historical'
            merged_data.append(historical_data)
        
        # Add recent CoinGecko data
        if not recent_data_df.empty:
            recent_processed = recent_data_df.copy()
            recent_processed['data_source'] = 'coingecko_recent'
            # Standardize column names
            if 'estimated_listing_date' in recent_processed.columns:
                recent_processed['first_seen_date'] = recent_processed['estimated_listing_date']
            merged_data.append(recent_processed)
        
        if merged_data:
            # Combine all data
            combined_df = pd.concat(merged_data, ignore_index=True, sort=False)
            
            # Remove duplicates based on symbol
            combined_df = combined_df.drop_duplicates(subset=['symbol'], keep='first')
            
            # Sort by first seen date
            if 'first_seen_date' in combined_df.columns:
                combined_df['first_seen_date'] = pd.to_datetime(combined_df['first_seen_date'], errors='coerce')
                combined_df = combined_df.sort_values('first_seen_date')
            
            # Save merged data
            output_file = f"{PROCESSED_DIR}/merged_crypto_listings.csv"
            combined_df.to_csv(output_file, index=False)
            logger.info(f"Saved merged data ({len(combined_df)} records) to {output_file}")
            
            return combined_df
        
        return pd.DataFrame()
    
    def generate_final_report(self, merged_df):
        """Generate final comprehensive report"""
        if merged_df.empty:
            return "No data available for report generation."
        
        # Calculate statistics
        total_listings = len(merged_df)
        
        # Date range
        if 'first_seen_date' in merged_df.columns:
            date_col = pd.to_datetime(merged_df['first_seen_date'], errors='coerce')
            date_range = f"{date_col.min().strftime('%Y-%m-%d')} to {date_col.max().strftime('%Y-%m-%d')}"
            
            # Yearly breakdown
            yearly_counts = date_col.dt.year.value_counts().sort_index()
        else:
            date_range = "Date information not available"
            yearly_counts = pd.Series()
        
        # Data sources
        source_counts = merged_df['data_source'].value_counts() if 'data_source' in merged_df.columns else pd.Series()
        
        # Sample data
        sample_columns = ['symbol', 'name', 'first_seen_date', 'data_source']
        available_columns = [col for col in sample_columns if col in merged_df.columns]
        sample_data = merged_df[available_columns].head(20)
        
        report = f"""
Comprehensive Cryptocurrency New Listings Report
===============================================

Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total New Listings: {total_listings}
Date Range: {date_range}

Data Sources:
{source_counts.to_string() if not source_counts.empty else 'No source information available'}

Listings by Year:
{yearly_counts.to_string() if not yearly_counts.empty else 'No yearly data available'}

Sample of Collected Data:
{sample_data.to_string(index=False) if not sample_data.empty else 'No sample data available'}

Files Generated:
- {NEW_LISTINGS_OUTPUT}
- {FULL_DATA_OUTPUT}
- {PROCESSED_DIR}/coingecko_recent_coins.csv
- {PROCESSED_DIR}/merged_crypto_listings.csv
- {PROCESSED_DIR}/final_report.txt

Data Size Estimate:
- Raw snapshots: ~{len(os.listdir(SNAPSHOTS_DIR)) * 0.5:.1f} MB
- Processed data: ~{total_listings * 0.0002:.1f} MB
        """
        
        # Save report
        report_file = f"{PROCESSED_DIR}/final_report.txt"
        with open(report_file, 'w') as f:
            f.write(report)
        
        return report
    
    def run_full_collection(self):
        """Run the complete data collection process"""
        logger.info("Starting complete cryptocurrency data collection process...")
        
        try:
            # Step 1: Download historical data
            print("Step 1: Downloading historical snapshots from CoinMarketCap...")
            historical_success = self.download_historical_data()
            
            if not historical_success:
                logger.warning("Historical data download failed, continuing with available data...")
            
            # Step 2: Detect new listings
            print("\nStep 2: Detecting new cryptocurrency listings...")
            new_listings_df, full_data_df = self.detect_new_listings()
            
            # Step 3: Fetch recent data from CoinGecko
            print("\nStep 3: Fetching recent data from CoinGecko...")
            recent_data_df = self.fetch_recent_data()
            
            # Step 4: Merge and process all data
            print("\nStep 4: Merging and processing all collected data...")
            merged_df = self.merge_and_process_data(new_listings_df, recent_data_df)
            
            # Step 5: Validate collected data
            print("\nStep 5: Validating collected data...")
            validation_passed, validation_report = self.data_validator.run_full_validation()

            # Step 6: Generate final report
            print("\nStep 6: Generating final report...")
            final_report = self.generate_final_report(merged_df)

            print("\n" + "="*60)
            print("DATA COLLECTION COMPLETE!")
            print("="*60)
            print(final_report)

            if not validation_passed:
                print("\n⚠ WARNING: Some validation checks failed. Check validation_report.txt for details.")
            else:
                print("\n✓ All validation checks passed!")

            return True
            
        except Exception as e:
            logger.error(f"Error during data collection: {str(e)}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Collect historical cryptocurrency data focusing on new listings')
    parser.add_argument('--download-only', action='store_true', help='Only download historical snapshots')
    parser.add_argument('--detect-only', action='store_true', help='Only detect new listings from existing snapshots')
    parser.add_argument('--coingecko-only', action='store_true', help='Only fetch recent data from CoinGecko')
    parser.add_argument('--validate-only', action='store_true', help='Only run data validation on existing files')
    
    args = parser.parse_args()
    
    collector = CryptoDataCollector()
    
    if args.download_only:
        collector.download_historical_data()
    elif args.detect_only:
        collector.detect_new_listings()
    elif args.coingecko_only:
        collector.fetch_recent_data()
    elif args.validate_only:
        passed, report = collector.data_validator.run_full_validation()
        print(report)
        print(f"\nValidation {'PASSED' if passed else 'FAILED'}")
    else:
        collector.run_full_collection()

if __name__ == "__main__":
    main()
