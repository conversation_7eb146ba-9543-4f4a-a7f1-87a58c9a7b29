"""
Multi-Chain Token Discovery System
Discovers and collects data for tokens across multiple blockchains
"""
import os
import sys
import time
import logging
import requests
import pandas as pd
from datetime import datetime
import json

from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/multi_chain_discovery.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MultiChainDiscovery:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
        
        # Supported chains and their native tokens
        self.chains = {
            'ethereum': {
                'name': 'Ethereum',
                'native_token': 'ETH',
                'dexscreener_id': 'ethereum',
                'example_tokens': ['USDC', 'USDT', 'WBTC', 'UNI', 'LINK']
            },
            'bsc': {
                'name': 'BNB Smart Chain',
                'native_token': 'BNB',
                'dexscreener_id': 'bsc',
                'example_tokens': ['CAKE', 'BUSD', 'XVS', 'ALPACA']
            },
            'polygon': {
                'name': 'Polygon',
                'native_token': 'MATIC',
                'dexscreener_id': 'polygon',
                'example_tokens': ['QUICK', 'GHST', 'DQUICK', 'WMATIC']
            },
            'arbitrum': {
                'name': 'Arbitrum',
                'native_token': 'ARB',
                'dexscreener_id': 'arbitrum',
                'example_tokens': ['GMX', 'MAGIC', 'GNS', 'RDNT']
            },
            'optimism': {
                'name': 'Optimism',
                'native_token': 'OP',
                'dexscreener_id': 'optimism',
                'example_tokens': ['SNX', 'THALES', 'LYRA']
            },
            'avalanche': {
                'name': 'Avalanche',
                'native_token': 'AVAX',
                'dexscreener_id': 'avalanche',
                'example_tokens': ['JOE', 'PNG', 'QI', 'XAVA']
            },
            'fantom': {
                'name': 'Fantom',
                'native_token': 'FTM',
                'dexscreener_id': 'fantom',
                'example_tokens': ['BOO', 'SPIRIT', 'LQDR', 'TOMB']
            },
            'pulsechain': {
                'name': 'PulseChain',
                'native_token': 'PLS',
                'dexscreener_id': 'pulsechain',
                'example_tokens': ['HEX', 'PLSX', 'INC', 'LOAN']
            },
            'base': {
                'name': 'Base',
                'native_token': 'ETH',
                'dexscreener_id': 'base',
                'example_tokens': ['BALD', 'TOSHI', 'BRETT']
            },
            'cronos': {
                'name': 'Cronos',
                'native_token': 'CRO',
                'dexscreener_id': 'cronos',
                'example_tokens': ['VVS', 'TONIC', 'SINGLE']
            }
        }
    
    def discover_chain_tokens(self, chain_id, limit=20):
        """Discover top tokens on a specific chain"""
        logger.info(f"Discovering tokens on {self.chains[chain_id]['name']}...")
        
        try:
            # Get top pairs for this chain
            url = f"https://api.dexscreener.com/latest/dex/pairs/{self.chains[chain_id]['dexscreener_id']}"
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                pairs = data.get('pairs', [])
                
                # Sort by volume and take top pairs
                sorted_pairs = sorted(pairs, key=lambda x: float(x.get('volume', {}).get('h24', 0)), reverse=True)
                
                tokens = []
                seen_tokens = set()
                
                for pair in sorted_pairs[:limit*2]:  # Get more pairs to find unique tokens
                    base_token = pair.get('baseToken', {})
                    quote_token = pair.get('quoteToken', {})
                    
                    for token in [base_token, quote_token]:
                        symbol = token.get('symbol', '').upper()
                        if symbol and symbol not in seen_tokens and symbol not in ['WETH', 'USDC', 'USDT', 'DAI']:
                            token_info = {
                                'symbol': symbol,
                                'name': token.get('name', ''),
                                'address': token.get('address', ''),
                                'chain': chain_id,
                                'chain_name': self.chains[chain_id]['name'],
                                'price_usd': float(pair.get('priceUsd', 0)),
                                'volume_24h': float(pair.get('volume', {}).get('h24', 0)),
                                'liquidity_usd': float(pair.get('liquidity', {}).get('usd', 0)),
                                'pair_address': pair.get('pairAddress', ''),
                                'dex': pair.get('dexId', ''),
                                'source': f'DexScreener_{chain_id}'
                            }
                            tokens.append(token_info)
                            seen_tokens.add(symbol)
                            
                            if len(tokens) >= limit:
                                break
                    
                    if len(tokens) >= limit:
                        break
                
                logger.info(f"Found {len(tokens)} tokens on {self.chains[chain_id]['name']}")
                return tokens
            
        except Exception as e:
            logger.error(f"Error discovering tokens on {chain_id}: {str(e)}")
        
        return []
    
    def discover_all_chains(self):
        """Discover tokens across all supported chains"""
        logger.info("Starting multi-chain token discovery...")
        
        all_tokens = []
        
        for chain_id, chain_info in self.chains.items():
            logger.info(f"Processing {chain_info['name']}...")
            
            try:
                chain_tokens = self.discover_chain_tokens(chain_id, limit=15)
                all_tokens.extend(chain_tokens)
                
                # Add native token info
                native_token = {
                    'symbol': chain_info['native_token'],
                    'name': chain_info['name'] + ' Native Token',
                    'chain': chain_id,
                    'chain_name': chain_info['name'],
                    'is_native': True,
                    'source': f'Native_{chain_id}'
                }
                all_tokens.append(native_token)
                
                time.sleep(2)  # Rate limiting between chains
                
            except Exception as e:
                logger.error(f"Failed to process {chain_info['name']}: {str(e)}")
                continue
        
        logger.info(f"Total tokens discovered across all chains: {len(all_tokens)}")
        return all_tokens
    
    def collect_multi_chain_data(self, tokens):
        """Collect current data for multi-chain tokens"""
        logger.info(f"Collecting data for {len(tokens)} multi-chain tokens...")
        
        all_data = []
        
        for token in tokens:
            symbol = token.get('symbol', '')
            chain = token.get('chain', '')
            
            if not symbol:
                continue
                
            logger.info(f"Collecting {symbol} on {chain}...")
            
            try:
                # Use DexScreener search with chain filter
                search_url = f"https://api.dexscreener.com/latest/dex/search/?q={symbol}"
                response = self.session.get(search_url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    pairs = data.get('pairs', [])
                    
                    # Filter pairs for the specific chain
                    chain_pairs = [p for p in pairs if p.get('chainId', '').lower() == chain.lower()]
                    
                    if chain_pairs:
                        # Take the pair with highest liquidity on this chain
                        best_pair = max(chain_pairs, key=lambda x: float(x.get('liquidity', {}).get('usd', 0)))
                        price = float(best_pair.get('priceUsd', 0))
                        
                        if price > 0:
                            token_data = {
                                'Symbol': symbol,
                                'Name': token.get('name', ''),
                                'Chain': chain,
                                'Chain_Name': token.get('chain_name', ''),
                                'Current_Price': price,
                                'Volume_24h': float(best_pair.get('volume', {}).get('h24', 0)),
                                'Liquidity_USD': float(best_pair.get('liquidity', {}).get('usd', 0)),
                                'Market_Cap': float(best_pair.get('marketCap', 0)),
                                'DEX': best_pair.get('dexId', ''),
                                'Pair_Address': best_pair.get('pairAddress', ''),
                                'Token_Address': token.get('address', ''),
                                'Is_Native': token.get('is_native', False),
                                'Source': 'DexScreener_MultiChain',
                                'Date': datetime.now().strftime('%Y-%m-%d'),
                                'Price_Change_24h': float(best_pair.get('priceChange', {}).get('h24', 0))
                            }
                            all_data.append(token_data)
                            logger.info(f"✅ {symbol} on {chain}: ${price}")
                
                time.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"Failed to collect {symbol} on {chain}: {str(e)}")
                continue
        
        if all_data:
            df = pd.DataFrame(all_data)
            output_file = f"{PROCESSED_DIR}/multi_chain_tokens_data.csv"
            df.to_csv(output_file, index=False)
            logger.info(f"Saved multi-chain token data: {len(df)} records to {output_file}")
            return df
        
        return pd.DataFrame()
    
    def run_comprehensive_multi_chain_discovery(self):
        """Run comprehensive multi-chain discovery"""
        logger.info("Starting comprehensive multi-chain token discovery...")
        
        # 1. Discover tokens across all chains
        discovered_tokens = self.discover_all_chains()
        
        # 2. Collect current data for all discovered tokens
        collected_data = self.collect_multi_chain_data(discovered_tokens)
        
        # 3. Generate comprehensive report
        report = self.generate_multi_chain_report(collected_data, discovered_tokens)
        
        return collected_data, report
    
    def generate_multi_chain_report(self, collected_data, all_tokens):
        """Generate comprehensive multi-chain report"""
        
        report_lines = [
            "MULTI-CHAIN TOKEN DISCOVERY REPORT",
            "=" * 50,
            f"Discovery Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "CHAINS ANALYZED:",
            *[f"  • {info['name']} ({chain_id})" for chain_id, info in self.chains.items()],
            "",
            "DISCOVERY RESULTS:",
            f"• Total tokens discovered: {len(all_tokens)}",
            f"• Successfully collected data: {len(collected_data)}",
            f"• Success rate: {len(collected_data)/len(all_tokens)*100:.1f}%" if all_tokens else "0%",
            ""
        ]
        
        if not collected_data.empty:
            # Chain breakdown
            chain_counts = collected_data['Chain_Name'].value_counts()
            report_lines.extend([
                "TOKENS BY BLOCKCHAIN:",
                *[f"  • {chain}: {count} tokens" for chain, count in chain_counts.items()],
                ""
            ])
            
            # Top tokens by market cap
            if 'Market_Cap' in collected_data.columns:
                top_by_mcap = collected_data[collected_data['Market_Cap'] > 0].nlargest(10, 'Market_Cap')
                if not top_by_mcap.empty:
                    report_lines.extend([
                        "TOP 10 TOKENS BY MARKET CAP:",
                        *[f"  • {row['Symbol']} ({row['Chain_Name']}): ${row['Market_Cap']:,.0f}" 
                          for _, row in top_by_mcap.iterrows()],
                        ""
                    ])
            
            # Top tokens by volume
            top_by_volume = collected_data.nlargest(10, 'Volume_24h')
            report_lines.extend([
                "TOP 10 TOKENS BY 24H VOLUME:",
                *[f"  • {row['Symbol']} ({row['Chain_Name']}): ${row['Volume_24h']:,.0f}" 
                  for _, row in top_by_volume.iterrows()],
                ""
            ])
            
            # Price gainers
            if 'Price_Change_24h' in collected_data.columns:
                gainers = collected_data[collected_data['Price_Change_24h'] > 0].nlargest(5, 'Price_Change_24h')
                if not gainers.empty:
                    report_lines.extend([
                        "TOP 5 PRICE GAINERS (24H):",
                        *[f"  • {row['Symbol']} ({row['Chain_Name']}): +{row['Price_Change_24h']:.2f}%" 
                          for _, row in gainers.iterrows()],
                        ""
                    ])
        
        report_lines.extend([
            "KEY INSIGHTS:",
            "✅ Multi-chain approach captures tokens missed by traditional APIs",
            "✅ DEX data provides real-time pricing for newer tokens",
            "✅ Cross-chain analysis reveals ecosystem trends",
            "",
            "RECOMMENDED ACTIONS:",
            "• Set up automated daily collection for promising tokens",
            "• Monitor cross-chain arbitrage opportunities",
            "• Track ecosystem growth and token migrations",
            "• Build historical datasets by running collection regularly",
            "",
            "FILES CREATED:",
            "• multi_chain_tokens_data.csv - Current multi-chain data",
            "• multi_chain_discovery_report.txt - This report",
            "• logs/multi_chain_discovery.log - Detailed logs"
        ])
        
        report_text = "\n".join(report_lines)
        
        with open("multi_chain_discovery_report.txt", 'w') as f:
            f.write(report_text)
        
        return report_text

if __name__ == "__main__":
    discovery = MultiChainDiscovery()
    
    print("🌐 MULTI-CHAIN TOKEN DISCOVERY SYSTEM")
    print("=" * 50)
    print("Discovering tokens across multiple blockchains:")
    for chain_id, info in discovery.chains.items():
        print(f"  • {info['name']} ({info['native_token']})")
    print()
    print("This will find tokens similar to PulseChain across all major chains!")
    print()
    
    data, report = discovery.run_comprehensive_multi_chain_discovery()
    
    print("\n" + "=" * 50)
    print("🎉 MULTI-CHAIN DISCOVERY COMPLETE!")
    print("=" * 50)
    print(report)
    
    if not data.empty:
        print(f"\n📊 Multi-chain data saved to: {PROCESSED_DIR}/multi_chain_tokens_data.csv")
        print(f"📋 Report saved to: multi_chain_discovery_report.txt")
    else:
        print("\n⚠️  No multi-chain data collected. Check logs for details.")
