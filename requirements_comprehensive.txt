# Comprehensive Cryptocurrency Data Collection Requirements
# Core data manipulation and analysis
pandas>=1.5.0
numpy>=1.21.0

# HTTP requests and web scraping
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# Cryptocurrency data sources
yfinance>=0.2.0
cryptocmd>=0.5.0

# Progress tracking and logging
tqdm>=4.64.0

# Date and time handling
python-dateutil>=2.8.0

# JSON handling (usually included with Python)
# json - built-in

# Threading and concurrency (built-in)
# concurrent.futures - built-in
# threading - built-in

# Optional: For advanced data analysis
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# Optional: For database storage
sqlalchemy>=1.4.0
# sqlite3 is built-in with Python

# Optional: For Excel export
openpyxl>=3.0.0
xlsxwriter>=3.0.0

# Optional: For enhanced web scraping
selenium>=4.5.0  # Only if needed for JavaScript-heavy sites
webdriver-manager>=3.8.0  # For automatic webdriver management

# Development and testing (optional)
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=5.0.0

# Memory optimization for large datasets
psutil>=5.9.0

# Configuration management
configparser  # built-in
