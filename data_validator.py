"""
Data Validation and Quality Checks for Cryptocurrency Data
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/data_validator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataValidator:
    def __init__(self):
        self.validation_results = {}
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def validate_snapshots_directory(self):
        """Validate the downloaded snapshots directory"""
        logger.info("Validating snapshots directory...")
        
        if not os.path.exists(SNAPSHOTS_DIR):
            logger.error(f"Snapshots directory does not exist: {SNAPSHOTS_DIR}")
            return False
        
        # Get all CSV files
        csv_files = [f for f in os.listdir(SNAPSHOTS_DIR) if f.endswith('.csv')]
        
        if not csv_files:
            logger.error("No CSV files found in snapshots directory")
            return False
        
        # Validate file naming convention (YYYYMMDD.csv)
        valid_files = []
        invalid_files = []
        
        for filename in csv_files:
            try:
                date_str = filename.replace('.csv', '')
                datetime.strptime(date_str, '%Y%m%d')
                valid_files.append(filename)
            except ValueError:
                invalid_files.append(filename)
        
        logger.info(f"Found {len(valid_files)} valid snapshot files")
        if invalid_files:
            logger.warning(f"Found {len(invalid_files)} files with invalid naming: {invalid_files}")
        
        # Check for date gaps
        dates = []
        for filename in valid_files:
            date_str = filename.replace('.csv', '')
            dates.append(datetime.strptime(date_str, '%Y%m%d'))
        
        dates.sort()
        gaps = self._find_date_gaps(dates)
        
        if gaps:
            logger.warning(f"Found {len(gaps)} date gaps in snapshots")
            for gap in gaps[:5]:  # Show first 5 gaps
                logger.warning(f"Gap: {gap['start']} to {gap['end']} ({gap['days']} days)")
        
        self.validation_results['snapshots'] = {
            'total_files': len(csv_files),
            'valid_files': len(valid_files),
            'invalid_files': len(invalid_files),
            'date_gaps': len(gaps),
            'date_range': f"{min(dates).strftime('%Y-%m-%d')} to {max(dates).strftime('%Y-%m-%d')}" if dates else "No dates"
        }
        
        return len(valid_files) > 0
    
    def _find_date_gaps(self, dates, expected_interval_days=7):
        """Find gaps in date sequence"""
        gaps = []
        
        for i in range(1, len(dates)):
            diff = (dates[i] - dates[i-1]).days
            if diff > expected_interval_days * 1.5:  # Allow some tolerance
                gaps.append({
                    'start': dates[i-1].strftime('%Y-%m-%d'),
                    'end': dates[i].strftime('%Y-%m-%d'),
                    'days': diff
                })
        
        return gaps
    
    def validate_snapshot_content(self, sample_size=10):
        """Validate content of snapshot files"""
        logger.info(f"Validating content of {sample_size} snapshot files...")
        
        csv_files = [f for f in os.listdir(SNAPSHOTS_DIR) if f.endswith('.csv')]
        
        if not csv_files:
            logger.error("No CSV files to validate")
            return False
        
        # Sample files for validation
        sample_files = csv_files[:sample_size] if len(csv_files) > sample_size else csv_files
        
        validation_stats = {
            'files_checked': 0,
            'valid_files': 0,
            'empty_files': 0,
            'corrupt_files': 0,
            'total_records': 0,
            'common_columns': set(),
            'issues': []
        }
        
        for filename in sample_files:
            filepath = os.path.join(SNAPSHOTS_DIR, filename)
            
            try:
                df = pd.read_csv(filepath)
                validation_stats['files_checked'] += 1
                
                if df.empty:
                    validation_stats['empty_files'] += 1
                    validation_stats['issues'].append(f"Empty file: {filename}")
                    continue
                
                # Check for required columns
                required_columns = ['Name', 'Symbol']  # Minimum required
                missing_columns = [col for col in required_columns if col not in df.columns]
                
                if missing_columns:
                    validation_stats['issues'].append(f"Missing columns in {filename}: {missing_columns}")
                else:
                    validation_stats['valid_files'] += 1
                
                validation_stats['total_records'] += len(df)
                validation_stats['common_columns'].update(df.columns)
                
                # Check for data quality issues
                if 'Symbol' in df.columns:
                    empty_symbols = df['Symbol'].isna().sum() + (df['Symbol'] == '').sum()
                    if empty_symbols > 0:
                        validation_stats['issues'].append(f"Empty symbols in {filename}: {empty_symbols}")
                
            except Exception as e:
                validation_stats['corrupt_files'] += 1
                validation_stats['issues'].append(f"Corrupt file {filename}: {str(e)}")
        
        logger.info(f"Content validation complete: {validation_stats['valid_files']}/{validation_stats['files_checked']} files valid")
        
        self.validation_results['content'] = validation_stats
        return validation_stats['valid_files'] > 0
    
    def validate_new_listings_data(self):
        """Validate the new listings detection results"""
        logger.info("Validating new listings data...")
        
        if not os.path.exists(NEW_LISTINGS_OUTPUT):
            logger.warning(f"New listings file does not exist: {NEW_LISTINGS_OUTPUT}")
            return False
        
        try:
            df = pd.read_csv(NEW_LISTINGS_OUTPUT)
            
            validation_stats = {
                'total_listings': len(df),
                'unique_symbols': df['symbol'].nunique() if 'symbol' in df.columns else 0,
                'date_range': None,
                'duplicate_symbols': 0,
                'missing_data': {},
                'issues': []
            }
            
            # Check for required columns
            required_columns = ['symbol', 'name', 'first_seen_date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                validation_stats['issues'].append(f"Missing required columns: {missing_columns}")
                return False
            
            # Check for duplicates
            duplicates = df['symbol'].duplicated().sum()
            validation_stats['duplicate_symbols'] = duplicates
            if duplicates > 0:
                validation_stats['issues'].append(f"Found {duplicates} duplicate symbols")
            
            # Check date range
            if 'first_seen_date' in df.columns:
                dates = pd.to_datetime(df['first_seen_date'], errors='coerce')
                valid_dates = dates.dropna()
                if len(valid_dates) > 0:
                    validation_stats['date_range'] = f"{valid_dates.min().strftime('%Y-%m-%d')} to {valid_dates.max().strftime('%Y-%m-%d')}"
                
                invalid_dates = len(dates) - len(valid_dates)
                if invalid_dates > 0:
                    validation_stats['issues'].append(f"Found {invalid_dates} invalid dates")
            
            # Check for missing data
            for col in df.columns:
                missing_count = df[col].isna().sum() + (df[col] == '').sum()
                if missing_count > 0:
                    validation_stats['missing_data'][col] = missing_count
            
            logger.info(f"New listings validation: {validation_stats['total_listings']} listings, {validation_stats['unique_symbols']} unique symbols")
            
            self.validation_results['new_listings'] = validation_stats
            return True
            
        except Exception as e:
            logger.error(f"Error validating new listings data: {str(e)}")
            return False
    
    def validate_data_consistency(self):
        """Check consistency across different data sources"""
        logger.info("Validating data consistency...")
        
        consistency_stats = {
            'symbol_overlap': 0,
            'date_consistency': True,
            'price_anomalies': 0,
            'issues': []
        }
        
        # Check if we have both historical and recent data
        files_to_check = [
            NEW_LISTINGS_OUTPUT,
            f"{PROCESSED_DIR}/coingecko_recent_coins.csv",
            f"{PROCESSED_DIR}/merged_crypto_listings.csv"
        ]
        
        available_files = [f for f in files_to_check if os.path.exists(f)]
        
        if len(available_files) < 2:
            logger.warning("Not enough data files for consistency check")
            return True
        
        # Load available data
        dataframes = {}
        for filepath in available_files:
            try:
                df = pd.read_csv(filepath)
                filename = os.path.basename(filepath)
                dataframes[filename] = df
            except Exception as e:
                logger.error(f"Error loading {filepath}: {str(e)}")
        
        # Check symbol overlaps
        if len(dataframes) >= 2:
            symbols_sets = {}
            for name, df in dataframes.items():
                if 'symbol' in df.columns:
                    symbols_sets[name] = set(df['symbol'].str.upper())
            
            if len(symbols_sets) >= 2:
                all_symbols = set()
                for symbols in symbols_sets.values():
                    all_symbols.update(symbols)
                
                overlaps = {}
                names = list(symbols_sets.keys())
                for i in range(len(names)):
                    for j in range(i+1, len(names)):
                        overlap = len(symbols_sets[names[i]] & symbols_sets[names[j]])
                        overlaps[f"{names[i]} & {names[j]}"] = overlap
                
                consistency_stats['symbol_overlaps'] = overlaps
        
        self.validation_results['consistency'] = consistency_stats
        return True
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        report_lines = [
            "Data Validation Report",
            "=" * 50,
            f"Validation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        # Snapshots validation
        if 'snapshots' in self.validation_results:
            stats = self.validation_results['snapshots']
            report_lines.extend([
                "Snapshots Directory Validation:",
                f"  Total files: {stats['total_files']}",
                f"  Valid files: {stats['valid_files']}",
                f"  Invalid files: {stats['invalid_files']}",
                f"  Date gaps: {stats['date_gaps']}",
                f"  Date range: {stats['date_range']}",
                ""
            ])
        
        # Content validation
        if 'content' in self.validation_results:
            stats = self.validation_results['content']
            report_lines.extend([
                "Content Validation:",
                f"  Files checked: {stats['files_checked']}",
                f"  Valid files: {stats['valid_files']}",
                f"  Empty files: {stats['empty_files']}",
                f"  Corrupt files: {stats['corrupt_files']}",
                f"  Total records: {stats['total_records']}",
                f"  Common columns: {', '.join(sorted(stats['common_columns']))}",
                ""
            ])
            
            if stats['issues']:
                report_lines.extend([
                    "Content Issues:",
                    *[f"  - {issue}" for issue in stats['issues'][:10]],  # Show first 10 issues
                    ""
                ])
        
        # New listings validation
        if 'new_listings' in self.validation_results:
            stats = self.validation_results['new_listings']
            report_lines.extend([
                "New Listings Validation:",
                f"  Total listings: {stats['total_listings']}",
                f"  Unique symbols: {stats['unique_symbols']}",
                f"  Duplicate symbols: {stats['duplicate_symbols']}",
                f"  Date range: {stats['date_range']}",
                ""
            ])
            
            if stats['missing_data']:
                report_lines.extend([
                    "Missing Data by Column:",
                    *[f"  {col}: {count}" for col, count in stats['missing_data'].items()],
                    ""
                ])
        
        report_text = "\n".join(report_lines)
        
        # Save report
        report_file = f"{PROCESSED_DIR}/validation_report.txt"
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        logger.info(f"Validation report saved to {report_file}")
        return report_text
    
    def run_full_validation(self):
        """Run complete validation suite"""
        logger.info("Starting full data validation...")
        
        validation_passed = True
        
        # Validate snapshots
        if not self.validate_snapshots_directory():
            validation_passed = False
        
        # Validate content
        if not self.validate_snapshot_content():
            validation_passed = False
        
        # Validate new listings
        if not self.validate_new_listings_data():
            logger.warning("New listings validation failed (file may not exist yet)")
        
        # Validate consistency
        self.validate_data_consistency()
        
        # Generate report
        report = self.generate_validation_report()
        
        logger.info(f"Full validation complete. Overall result: {'PASSED' if validation_passed else 'FAILED'}")
        
        return validation_passed, report

if __name__ == "__main__":
    validator = DataValidator()
    passed, report = validator.run_full_validation()
    
    print(report)
    print(f"\nValidation {'PASSED' if passed else 'FAILED'}")
