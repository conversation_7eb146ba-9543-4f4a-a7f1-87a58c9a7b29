"""
CoinGecko API Data Fetcher
Fetches recent cryptocurrency data and new listings from CoinGecko's free API
"""
import os
import time
import requests
import pandas as pd
from datetime import datetime, timedelta
import logging
from tqdm import tqdm
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/coingecko_fetcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CoinGeckoFetcher:
    def __init__(self):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Create directories
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def make_request(self, endpoint, params=None):
        """Make API request with rate limiting and error handling"""
        url = f"{self.base_url}/{endpoint}"
        
        for attempt in range(COINGECKO_MAX_RETRIES):
            try:
                response = self.session.get(url, params=params)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limited
                    wait_time = COINGECKO_API_DELAY * (attempt + 1) * 2
                    logger.warning(f"Rate limited, waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    logger.warning(f"API request failed with status {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Request error (attempt {attempt + 1}): {str(e)}")
                
            if attempt < COINGECKO_MAX_RETRIES - 1:
                time.sleep(COINGECKO_API_DELAY)
        
        logger.error(f"Failed to fetch data from {endpoint} after {COINGECKO_MAX_RETRIES} attempts")
        return None
    
    def get_all_coins_list(self):
        """Get complete list of all coins from CoinGecko"""
        logger.info("Fetching complete coins list from CoinGecko...")
        
        coins_data = self.make_request("coins/list", params={"include_platform": "false"})
        
        if coins_data:
            df = pd.DataFrame(coins_data)
            logger.info(f"Retrieved {len(df)} coins from CoinGecko")
            return df
        
        return pd.DataFrame()
    
    def get_new_coins(self):
        """Get recently added coins (last 30 days)"""
        logger.info("Fetching new coins from CoinGecko...")
        
        # CoinGecko doesn't have a direct "new coins" endpoint in free tier
        # We'll use the coins list and try to identify recent additions
        
        # Get trending coins as a proxy for new/popular coins
        trending_data = self.make_request("search/trending")
        
        new_coins = []
        if trending_data and 'coins' in trending_data:
            for coin in trending_data['coins']:
                coin_info = coin.get('item', {})
                new_coins.append({
                    'id': coin_info.get('id'),
                    'symbol': coin_info.get('symbol', '').upper(),
                    'name': coin_info.get('name'),
                    'market_cap_rank': coin_info.get('market_cap_rank'),
                    'source': 'trending'
                })
        
        # Also try to get coins from markets endpoint with recent activity
        time.sleep(COINGECKO_API_DELAY)
        
        markets_data = self.make_request("coins/markets", params={
            "vs_currency": "usd",
            "order": "market_cap_desc",
            "per_page": 250,
            "page": 1,
            "sparkline": "false"
        })
        
        if markets_data:
            for coin in markets_data:
                # Look for coins with recent activity or low market cap rank (potentially new)
                if coin.get('market_cap_rank', 0) > 500:  # Focus on newer/smaller coins
                    new_coins.append({
                        'id': coin.get('id'),
                        'symbol': coin.get('symbol', '').upper(),
                        'name': coin.get('name'),
                        'market_cap_rank': coin.get('market_cap_rank'),
                        'current_price': coin.get('current_price'),
                        'market_cap': coin.get('market_cap'),
                        'total_volume': coin.get('total_volume'),
                        'price_change_percentage_24h': coin.get('price_change_percentage_24h'),
                        'source': 'markets'
                    })
        
        return pd.DataFrame(new_coins)
    
    def get_coin_history_dates(self, coin_id):
        """Get the earliest available data date for a coin (indicates listing date)"""
        try:
            # Get coin info which includes first data date
            coin_info = self.make_request(f"coins/{coin_id}")
            
            if coin_info and 'market_data' in coin_info:
                # Look for earliest price data
                ath_date = coin_info['market_data'].get('ath_date', {}).get('usd')
                if ath_date:
                    return datetime.fromisoformat(ath_date.replace('Z', '+00:00')).date()
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting history dates for {coin_id}: {str(e)}")
            return None
    
    def enrich_new_coins_data(self, new_coins_df):
        """Enrich new coins data with additional information"""
        if new_coins_df.empty:
            return new_coins_df
        
        enriched_data = []
        
        for _, coin in tqdm(new_coins_df.iterrows(), total=len(new_coins_df), desc="Enriching coin data"):
            coin_id = coin.get('id')
            if not coin_id:
                continue
            
            # Get additional coin information
            coin_info = self.make_request(f"coins/{coin_id}")
            
            if coin_info:
                enriched_coin = coin.to_dict()
                
                # Add additional fields
                enriched_coin.update({
                    'description': coin_info.get('description', {}).get('en', '')[:200] + '...' if coin_info.get('description', {}).get('en') else '',
                    'homepage': coin_info.get('links', {}).get('homepage', [None])[0],
                    'blockchain_site': coin_info.get('links', {}).get('blockchain_site', [None])[0],
                    'genesis_date': coin_info.get('genesis_date'),
                    'market_cap_rank': coin_info.get('market_cap_rank'),
                    'coingecko_rank': coin_info.get('coingecko_rank'),
                    'coingecko_score': coin_info.get('coingecko_score'),
                    'community_score': coin_info.get('community_score'),
                    'developer_score': coin_info.get('developer_score'),
                    'public_interest_score': coin_info.get('public_interest_score')
                })
                
                # Try to get first data date
                first_data_date = self.get_coin_history_dates(coin_id)
                if first_data_date:
                    enriched_coin['estimated_listing_date'] = first_data_date.strftime('%Y-%m-%d')
                
                enriched_data.append(enriched_coin)
            
            # Rate limiting
            time.sleep(COINGECKO_API_DELAY)
        
        return pd.DataFrame(enriched_data)
    
    def fetch_and_save_recent_data(self):
        """Main method to fetch and save recent cryptocurrency data"""
        logger.info("Starting CoinGecko data collection...")
        
        # Get new/trending coins
        new_coins_df = self.get_new_coins()
        
        if not new_coins_df.empty:
            logger.info(f"Found {len(new_coins_df)} potential new coins")
            
            # Enrich with additional data
            enriched_df = self.enrich_new_coins_data(new_coins_df)
            
            # Save results
            output_file = f"{PROCESSED_DIR}/coingecko_recent_coins.csv"
            enriched_df.to_csv(output_file, index=False)
            logger.info(f"Saved {len(enriched_df)} enriched coin records to {output_file}")
            
            return enriched_df
        else:
            logger.warning("No new coins data retrieved from CoinGecko")
            return pd.DataFrame()
    
    def get_market_data_for_symbols(self, symbols_list):
        """Get current market data for a list of symbols"""
        if not symbols_list:
            return pd.DataFrame()
        
        # Convert symbols to CoinGecko IDs (this is a simplified approach)
        # In practice, you'd need a mapping from symbols to CoinGecko IDs
        
        market_data = []
        
        # Get market data in batches
        batch_size = 250  # CoinGecko API limit
        for i in range(0, len(symbols_list), batch_size):
            batch_symbols = symbols_list[i:i+batch_size]
            
            # This is a simplified approach - in reality you'd need proper symbol-to-ID mapping
            markets_data = self.make_request("coins/markets", params={
                "vs_currency": "usd",
                "order": "market_cap_desc",
                "per_page": batch_size,
                "page": i//batch_size + 1,
                "sparkline": "false"
            })
            
            if markets_data:
                for coin in markets_data:
                    if coin.get('symbol', '').upper() in [s.upper() for s in batch_symbols]:
                        market_data.append(coin)
            
            time.sleep(COINGECKO_API_DELAY)
        
        return pd.DataFrame(market_data)

if __name__ == "__main__":
    fetcher = CoinGeckoFetcher()
    
    print("Fetching recent cryptocurrency data from CoinGecko...")
    recent_data = fetcher.fetch_and_save_recent_data()
    
    if not recent_data.empty:
        print(f"\nSuccessfully fetched data for {len(recent_data)} coins")
        print("\nSample of recent coins:")
        print(recent_data[['symbol', 'name', 'market_cap_rank']].head(10).to_string(index=False))
    else:
        print("No recent data retrieved. Check logs for details.")
