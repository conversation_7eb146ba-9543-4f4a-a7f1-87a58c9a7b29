"""
Dedicated PulseChain and HEX data collector
Attempts to collect historical data from multiple free sources
"""
import os
import sys
import time
import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from tqdm import tqdm

from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/pulsechain_hex_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PulseChainHEXCollector:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        os.makedirs(PROCESSED_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def collect_coingecko_data(self):
        """Collect PulseChain and HEX data from CoinGecko API"""
        logger.info("Collecting PulseChain and HEX data from CoinGecko...")
        
        # Token configurations with CoinGecko IDs
        tokens = {
            'pulsechain': {
                'id': 'pulsechain',
                'symbol': 'PLS',
                'name': 'PulseChain'
            },
            'hex-pulsechain': {
                'id': 'hex-pulsechain',
                'symbol': 'HEX',
                'name': 'HEX (PulseChain)'
            }
        }
        
        all_data = []
        base_url = "https://api.coingecko.com/api/v3"
        
        for token_key, token_info in tokens.items():
            logger.info(f"Fetching data for {token_info['name']} ({token_info['symbol']})...")
            
            try:
                # Get maximum available historical data
                url = f"{base_url}/coins/{token_info['id']}/market_chart"
                params = {
                    'vs_currency': 'usd',
                    'days': 'max',  # Get all available data
                    'interval': 'daily'
                }
                
                response = self.session.get(url, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    prices = data.get('prices', [])
                    market_caps = data.get('market_caps', [])
                    volumes = data.get('total_volumes', [])
                    
                    logger.info(f"Found {len(prices)} price points for {token_info['symbol']}")
                    
                    # Process the data
                    for i, (timestamp, price) in enumerate(prices):
                        date = datetime.fromtimestamp(timestamp / 1000)
                        
                        # Get corresponding market cap and volume
                        market_cap = market_caps[i][1] if i < len(market_caps) else None
                        volume = volumes[i][1] if i < len(volumes) else None
                        
                        record = {
                            'Date': date.strftime('%Y-%m-%d'),
                            'Symbol': token_info['symbol'],
                            'Name': token_info['name'],
                            'Close': price,
                            'Market_Cap': market_cap,
                            'Volume': volume,
                            'Source': 'CoinGecko',
                            'Timestamp': timestamp
                        }
                        all_data.append(record)
                    
                    logger.info(f"✓ Collected {len(prices)} records for {token_info['symbol']}")
                
                elif response.status_code == 429:
                    logger.warning(f"Rate limited for {token_info['symbol']}, waiting...")
                    time.sleep(60)  # Wait 1 minute for rate limit
                    continue
                
                else:
                    logger.warning(f"Failed to fetch {token_info['symbol']}: HTTP {response.status_code}")
                
                # Rate limiting - be respectful to free API
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"Error fetching {token_info['symbol']}: {str(e)}")
                continue
        
        if all_data:
            df = pd.DataFrame(all_data)
            output_file = f"{PROCESSED_DIR}/coingecko_pulsechain_hex_data.csv"
            df.to_csv(output_file, index=False)
            logger.info(f"Saved CoinGecko data: {len(df)} records to {output_file}")
            return df
        else:
            logger.warning("No CoinGecko data collected")
            return pd.DataFrame()
    
    def collect_cryptocmd_data(self):
        """Try to collect HEX data using CryptoCMD"""
        logger.info("Attempting to collect HEX data using CryptoCMD...")
        
        try:
            from cryptocmd import CmcScraper
            
            # Try different variations of HEX
            hex_variations = ['HEX']
            all_data = []
            
            start_date = START_DATE.strftime("%d-%m-%Y")
            end_date = END_DATE.strftime("%d-%m-%Y")
            
            for hex_symbol in hex_variations:
                try:
                    logger.info(f"Trying CryptoCMD for {hex_symbol}...")
                    scraper = CmcScraper(hex_symbol, start_date, end_date)
                    df = scraper.get_dataframe()
                    
                    if not df.empty:
                        df['Symbol'] = hex_symbol
                        df['Source'] = 'CryptoCMD'
                        all_data.append(df)
                        logger.info(f"✓ CryptoCMD: Collected {len(df)} records for {hex_symbol}")
                    else:
                        logger.info(f"No data found for {hex_symbol}")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"CryptoCMD failed for {hex_symbol}: {str(e)}")
                    continue
            
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                output_file = f"{PROCESSED_DIR}/cryptocmd_hex_data.csv"
                combined_df.to_csv(output_file, index=False)
                logger.info(f"Saved CryptoCMD data: {len(combined_df)} records to {output_file}")
                return combined_df
            else:
                logger.info("No CryptoCMD data collected")
                return pd.DataFrame()
                
        except ImportError:
            logger.warning("CryptoCMD library not available. Install with: pip install cryptocmd")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error in CryptoCMD collection: {str(e)}")
            return pd.DataFrame()
    
    def collect_all_pulsechain_hex_data(self):
        """Collect PulseChain and HEX data from all available sources"""
        logger.info("Starting comprehensive PulseChain and HEX data collection...")
        
        all_datasets = []
        
        # Method 1: CoinGecko API
        coingecko_data = self.collect_coingecko_data()
        if not coingecko_data.empty:
            all_datasets.append(coingecko_data)
        
        # Method 2: CryptoCMD
        cryptocmd_data = self.collect_cryptocmd_data()
        if not cryptocmd_data.empty:
            all_datasets.append(cryptocmd_data)
        
        # Combine all datasets
        if all_datasets:
            logger.info("Combining data from all sources...")
            combined_df = pd.concat(all_datasets, ignore_index=True, sort=False)
            
            # Remove duplicates based on Date and Symbol
            combined_df = combined_df.drop_duplicates(subset=['Date', 'Symbol'], keep='first')
            
            # Sort by Symbol and Date
            combined_df = combined_df.sort_values(['Symbol', 'Date'])
            
            # Save combined dataset
            output_file = f"{PROCESSED_DIR}/comprehensive_pulsechain_hex_data.csv"
            combined_df.to_csv(output_file, index=False)
            
            # Generate summary
            summary = self.generate_summary(combined_df)
            
            logger.info(f"✓ Comprehensive PulseChain/HEX collection complete: {len(combined_df)} total records")
            return combined_df, summary
        else:
            logger.error("No PulseChain/HEX data collected from any source")
            return pd.DataFrame(), "No data collected"
    
    def generate_summary(self, df):
        """Generate summary of collected PulseChain and HEX data"""
        if df.empty:
            return "No PulseChain/HEX data available"
        
        summary_lines = [
            "PulseChain and HEX Data Collection Summary",
            "=" * 50,
            f"Total Records: {len(df):,}",
            ""
        ]
        
        # Date range
        if 'Date' in df.columns:
            date_min = df['Date'].min()
            date_max = df['Date'].max()
            summary_lines.extend([
                f"Date Range: {date_min} to {date_max}",
                ""
            ])
        
        # Token breakdown
        if 'Symbol' in df.columns:
            token_counts = df['Symbol'].value_counts()
            summary_lines.extend([
                "Records by Token:",
                *[f"  {token}: {count:,}" for token, count in token_counts.items()],
                ""
            ])
        
        # Source breakdown
        if 'Source' in df.columns:
            source_counts = df['Source'].value_counts()
            summary_lines.extend([
                "Records by Source:",
                *[f"  {source}: {count:,}" for source, count in source_counts.items()],
                ""
            ])
        
        summary_text = "\n".join(summary_lines)
        
        # Save summary
        with open(f"{PROCESSED_DIR}/pulsechain_hex_summary.txt", 'w') as f:
            f.write(summary_text)
        
        return summary_text

if __name__ == "__main__":
    collector = PulseChainHEXCollector()
    
    print("🚀 PulseChain and HEX Data Collector")
    print("=" * 40)
    print("This script will attempt to collect historical data for:")
    print("- PulseChain (PLS)")
    print("- HEX (PulseChain)")
    print()
    print("Data sources:")
    print("- CoinGecko API (free tier)")
    print("- CryptoCMD (CoinMarketCap scraper)")
    print()
    
    combined_data, summary = collector.collect_all_pulsechain_hex_data()
    
    print("\n" + "="*50)
    print("🎉 COLLECTION COMPLETE!")
    print("="*50)
    print(summary)
    
    if not combined_data.empty:
        print(f"\n📊 Data saved to: {PROCESSED_DIR}/comprehensive_pulsechain_hex_data.csv")
        print(f"📋 Summary saved to: {PROCESSED_DIR}/pulsechain_hex_summary.txt")
    else:
        print("\n⚠️  No data was collected. Check the logs for details.")
