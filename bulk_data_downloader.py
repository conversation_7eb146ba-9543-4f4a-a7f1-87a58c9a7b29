"""
Bulk Cryptocurrency Data Downloader
Leverages free, bulk CSV data sources to download extensive historical data.
"""

import os
import pandas as pd
import requests
from io import StringIO
import logging

logger = logging.getLogger(__name__)

class BulkDataDownloader:
    def __init__(self, data_dir="data/bulk"):
        self.data_dir = data_dir
        os.makedirs(self.data_dir, exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def download_cryptodatadownload(self):
        """
        Downloads daily historical data from CryptoDataDownload.
        This source provides bulk CSVs for various exchanges.
        We will start with a major exchange like Binance for broad coverage.
        """
        logger.info("Downloading bulk data from CryptoDataDownload...")
        # URL for daily data from Binance for BTC/USDT. 
        # This is a sample, we can expand this to loop through more pairs.
        url = "https://www.cryptodatadownload.com/cdd/Binance_BTCUSDT_d.csv"
        
        try:
            response = self.session.get(url, timeout=60)
            if response.status_code == 200:
                # The first line is a disclaimer, so we skip it.
                csv_data = StringIO(response.text)
                df = pd.read_csv(csv_data, skiprows=1)
                
                # Clean and standardize the data
                df = df.rename(columns={"Date": "date", "Symbol": "symbol", "Open": "open", "High": "high", "Low": "low", "Close": "close", "Volume USDT": "volume"})
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values(by='date', ascending=True)

                # Save the data
                output_file = os.path.join(self.data_dir, "cryptodatadownload_binance_daily.csv")
                df.to_csv(output_file, index=False)
                logger.info(f"Successfully downloaded and saved {len(df)} records from CryptoDataDownload to {output_file}")
                return df
            else:
                logger.error(f"Failed to download from CryptoDataDownload. Status code: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"An error occurred while downloading from CryptoDataDownload: {e}")
            return None

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    downloader = BulkDataDownloader()
    downloader.download_cryptodatadownload()
