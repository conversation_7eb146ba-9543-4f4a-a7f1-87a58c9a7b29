"""
New Cryptocurrency Listings Detector
Analyzes historical snapshots to identify newly listed cryptocurrencies
"""
import os
import pandas as pd
from datetime import datetime
import logging
from tqdm import tqdm
from config import *

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/new_listings_detector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NewListingsDetector:
    def __init__(self):
        self.snapshots_dir = SNAPSHOTS_DIR
        self.processed_dir = PROCESSED_DIR
        os.makedirs(self.processed_dir, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)
    
    def get_snapshot_files(self):
        """Get all snapshot files sorted by date"""
        files = []
        for filename in os.listdir(self.snapshots_dir):
            if filename.endswith('.csv'):
                try:
                    date_str = filename.replace('.csv', '')
                    date = datetime.strptime(date_str, '%Y%m%d')
                    files.append((date, filename))
                except ValueError:
                    logger.warning(f"Skipping file with invalid date format: {filename}")
        
        # Sort by date
        files.sort(key=lambda x: x[0])
        return files
    
    def clean_dataframe(self, df, date_str):
        """Clean and standardize dataframe columns"""
        try:
            # Common column name variations
            column_mapping = {
                'Name': 'name',
                'Symbol': 'symbol', 
                'Market Cap': 'market_cap',
                'Price': 'price',
                'Volume (24h)': 'volume_24h',
                'Circulating Supply': 'circulating_supply',
                'Change (24h)': 'change_24h',
                'Change (7d)': 'change_7d',
                '#': 'rank',
                'Rank': 'rank'
            }
            
            # Rename columns to standard format
            df_cleaned = df.copy()
            for old_name, new_name in column_mapping.items():
                if old_name in df_cleaned.columns:
                    df_cleaned = df_cleaned.rename(columns={old_name: new_name})
            
            # Ensure required columns exist
            required_columns = ['name', 'symbol']
            for col in required_columns:
                if col not in df_cleaned.columns:
                    logger.warning(f"Missing required column '{col}' in {date_str}")
                    return None
            
            # Clean symbol column - remove whitespace and convert to uppercase
            df_cleaned['symbol'] = df_cleaned['symbol'].astype(str).str.strip().str.upper()
            df_cleaned['name'] = df_cleaned['name'].astype(str).str.strip()
            
            # Remove rows with empty symbols or names
            df_cleaned = df_cleaned[
                (df_cleaned['symbol'] != '') & 
                (df_cleaned['symbol'] != 'NAN') &
                (df_cleaned['name'] != '') & 
                (df_cleaned['name'] != 'NAN')
            ]
            
            # Add date column
            df_cleaned['snapshot_date'] = date_str
            
            return df_cleaned
            
        except Exception as e:
            logger.error(f"Error cleaning dataframe for {date_str}: {str(e)}")
            return None
    
    def detect_new_listings(self):
        """Detect new cryptocurrency listings by comparing consecutive snapshots"""
        snapshot_files = self.get_snapshot_files()
        
        if len(snapshot_files) < 2:
            logger.error("Need at least 2 snapshots to detect new listings")
            return None
        
        logger.info(f"Processing {len(snapshot_files)} snapshots for new listings detection")
        
        new_listings = []
        previous_symbols = set()
        all_data = []
        
        for i, (date, filename) in enumerate(tqdm(snapshot_files, desc="Processing snapshots")):
            filepath = os.path.join(self.snapshots_dir, filename)
            date_str = date.strftime('%Y-%m-%d')
            
            try:
                # Read snapshot
                df = pd.read_csv(filepath)
                df_cleaned = self.clean_dataframe(df, date_str)
                
                if df_cleaned is None or df_cleaned.empty:
                    logger.warning(f"Skipping empty or invalid snapshot: {filename}")
                    continue
                
                # Get current symbols
                current_symbols = set(df_cleaned['symbol'].unique())
                
                # Find new symbols (not in previous snapshot)
                if i > 0:  # Skip first snapshot as we have no previous to compare
                    new_symbols = current_symbols - previous_symbols
                    
                    for symbol in new_symbols:
                        # Get coin data for this new symbol
                        coin_data = df_cleaned[df_cleaned['symbol'] == symbol].iloc[0]
                        
                        new_listing = {
                            'first_seen_date': date_str,
                            'symbol': symbol,
                            'name': coin_data['name'],
                            'first_seen_price': coin_data.get('price', 'N/A'),
                            'first_seen_market_cap': coin_data.get('market_cap', 'N/A'),
                            'first_seen_rank': coin_data.get('rank', 'N/A'),
                            'first_seen_volume_24h': coin_data.get('volume_24h', 'N/A')
                        }
                        new_listings.append(new_listing)
                
                # Store all data for comprehensive dataset
                all_data.append(df_cleaned)
                
                # Update previous symbols for next iteration
                previous_symbols = current_symbols
                
            except Exception as e:
                logger.error(f"Error processing {filename}: {str(e)}")
                continue
        
        # Create results DataFrames
        new_listings_df = pd.DataFrame(new_listings)
        
        # Combine all historical data
        if all_data:
            full_data_df = pd.concat(all_data, ignore_index=True)
        else:
            full_data_df = pd.DataFrame()
        
        return new_listings_df, full_data_df
    
    def save_results(self, new_listings_df, full_data_df):
        """Save detection results to CSV files"""
        try:
            # Save new listings
            if not new_listings_df.empty:
                new_listings_df.to_csv(NEW_LISTINGS_OUTPUT, index=False)
                logger.info(f"Saved {len(new_listings_df)} new listings to {NEW_LISTINGS_OUTPUT}")
            else:
                logger.warning("No new listings detected")
            
            # Save full historical data
            if not full_data_df.empty:
                full_data_df.to_csv(FULL_DATA_OUTPUT, index=False)
                logger.info(f"Saved full historical data ({len(full_data_df)} records) to {FULL_DATA_OUTPUT}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")
            return False
    
    def generate_summary_report(self, new_listings_df):
        """Generate a summary report of new listings"""
        if new_listings_df.empty:
            return "No new listings detected."
        
        # Basic statistics
        total_new_listings = len(new_listings_df)
        date_range = f"{new_listings_df['first_seen_date'].min()} to {new_listings_df['first_seen_date'].max()}"
        
        # Listings per year
        new_listings_df['year'] = pd.to_datetime(new_listings_df['first_seen_date']).dt.year
        yearly_counts = new_listings_df['year'].value_counts().sort_index()
        
        # Top months for new listings
        new_listings_df['month'] = pd.to_datetime(new_listings_df['first_seen_date']).dt.strftime('%Y-%m')
        monthly_counts = new_listings_df['month'].value_counts().head(10)
        
        report = f"""
New Cryptocurrency Listings Summary Report
==========================================

Total New Listings Detected: {total_new_listings}
Date Range: {date_range}

Listings by Year:
{yearly_counts.to_string()}

Top 10 Months for New Listings:
{monthly_counts.to_string()}

Sample of Recent New Listings:
{new_listings_df.head(10)[['first_seen_date', 'symbol', 'name']].to_string(index=False)}
        """
        
        return report

if __name__ == "__main__":
    detector = NewListingsDetector()
    
    print("Detecting new cryptocurrency listings...")
    new_listings_df, full_data_df = detector.detect_new_listings()
    
    if new_listings_df is not None:
        # Save results
        detector.save_results(new_listings_df, full_data_df)
        
        # Generate and display summary
        summary = detector.generate_summary_report(new_listings_df)
        print(summary)
        
        # Save summary report
        with open(f"{PROCESSED_DIR}/summary_report.txt", 'w') as f:
            f.write(summary)
    else:
        print("Failed to detect new listings. Check logs for details.")
